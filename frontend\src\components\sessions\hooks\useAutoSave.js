/**
 * Custom hook for handling auto-save functionality in EECP sessions.
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import { AUTO_SAVE_INTERVAL, UPDATE_INTERVAL } from '../constants/waveformConstants';

/**
 * Custom hook for managing auto-save functionality with visual feedback.
 * Handles periodic saving of session data and displays save status to users.
 *
 * @param {boolean} isActive - Whether auto-save should be active
 * @param {Function} saveCallback - Function to call when saving data
 * @param {Object} dataToSave - Data object to save
 * @returns {Object} Auto-save state and control functions
 */
const useAutoSave = (isActive, saveCallback, dataToSave) => {
    const [lastSaved, setLastSaved] = useState('Just now');
    const [isSaving, setIsSaving] = useState(false);
    const lastSaveRef = useRef(Date.now());
    const intervalRef = useRef(null);

    /**
     * Performs the actual save operation.
     * Handles both auto-save and manual save scenarios.
     */
    const performSave = useCallback(async () => {
        if (!saveCallback || !dataToSave) return;

        setIsSaving(true);
        try {
            await saveCallback({
                timestamp: new Date().toISOString(),
                data: dataToSave
            });

            lastSaveRef.current = Date.now();
            setLastSaved('Just now');

            console.log('Auto-save completed:', {
                timestamp: new Date().toISOString(),
                dataToSave
            });
        } catch (error) {
            console.error('Auto-save failed:', error);
            setLastSaved('Save failed');
        } finally {
            setIsSaving(false);
        }
    }, [saveCallback, dataToSave]);

    /**
     * Handles manual save triggered by user action.
     */
    const handleManualSave = useCallback(async () => {
        await performSave();
    }, [performSave]);

    /**
     * Updates the "last saved" display text based on elapsed time.
     */
    const updateLastSavedText = useCallback(() => {
        if (isActive) {
            const now = Date.now();
            const timeSinceLastSave = now - lastSaveRef.current;

            if (timeSinceLastSave < 5000) {
                setLastSaved('Just now');
            } else if (timeSinceLastSave < 60000) {
                const secondsAgo = Math.floor(timeSinceLastSave / 1000);
                setLastSaved(`${secondsAgo}s ago`);
            } else {
                const minutesAgo = Math.floor(timeSinceLastSave / 60000);
                setLastSaved(`${minutesAgo}m ago`);
            }
        }
    }, [isActive]);

    // Set up auto-save interval and status updates
    useEffect(() => {
        if (!isActive) {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
            }
            return;
        }

        // Set up the main interval for auto-save and status updates
        intervalRef.current = setInterval(() => {
            const now = Date.now();
            const timeSinceLastSave = now - lastSaveRef.current;

            // Auto-save if enough time has passed
            if (timeSinceLastSave >= AUTO_SAVE_INTERVAL) {
                performSave();
            } else {
                // Just update the display text
                updateLastSavedText();
            }
        }, UPDATE_INTERVAL);

        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }
        };
    }, [isActive, performSave, updateLastSavedText]);

    return {
        lastSaved,
        isSaving,
        handleManualSave,
        performSave
    };
};

export default useAutoSave;