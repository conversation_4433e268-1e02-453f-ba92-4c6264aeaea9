
/**
 * Component for EECP session control buttons.
 */

import { FaPlay, FaPause } from 'react-icons/fa';

const SESSION_STATUS = {
    READY: 'ready',
    RUNNING: 'running',
    PAUSED: 'paused'
};

/**
 * Renders session control buttons based on current status.
 * 
 * @param {Object} props - Component props
 * @param {string} props.sessionStatus - Current session status
 * @param {Function} props.onStartSession - Start session handler
 * @param {Function} props.onPauseSession - Pause session handler
 * @returns {JSX.Element} Session controls component
 */
const SessionControls = ({ sessionStatus, onStartSession, onPauseSession }) => {
    return (
        <div className="mt-6 bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
                <div>
                    <h2 className="text-lg font-medium text-gray-900">Session Controls</h2>
                    <p className="mt-1 text-sm text-gray-500">
                        Control the EECP therapy session
                    </p>
                </div>
                <div className="flex space-x-2">
                    {sessionStatus === SESSION_STATUS.READY && (
                        <button
                            type="button"
                            onClick={onStartSession}
                            className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                        >
                            <FaPlay className="mr-1" /> Start Session
                        </button>
                    )}
                    {sessionStatus === SESSION_STATUS.RUNNING && (
                        <button
                            type="button"
                            onClick={onPauseSession}
                            className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                        >
                            <FaPause className="mr-1" /> Pause Session
                        </button>
                    )}
                    {sessionStatus === SESSION_STATUS.PAUSED && (
                        <button
                            type="button"
                            onClick={onStartSession}
                            className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                        >
                            <FaPlay className="mr-1" /> Resume Session
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};

export default SessionControls;