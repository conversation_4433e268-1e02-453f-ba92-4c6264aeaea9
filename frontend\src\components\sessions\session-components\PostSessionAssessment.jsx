/**
 * Component for post-session vitals recording and session actions.
 */

import { FaMicrophone, FaSave } from 'react-icons/fa';

/**
 * Handles post-session assessment and actions.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.vitals - Vitals data object
 * @param {Function} props.onVitalChange - Vital signs change handler
 * @param {Function} props.onToggleAIScribe - AI Scribe toggle handler
 * @param {Function} props.onSaveSession - Save session handler
 * @returns {JSX.Element} Post-session assessment form
 */
const PostSessionAssessment = ({ vitals, onVitalChange, onToggleAIScribe, onSaveSession }) => {
    /**
     * Handles vital signs input changes.
     */
    const handleVitalChange = (e) => {
        const { name, value } = e.target;
        onVitalChange(name, value);
    };

    return (
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6">
                <h2 className="text-lg font-medium text-gray-900">Post-Session Assessment</h2>
                <p className="mt-1 text-sm text-gray-500">Record vitals after completing the session</p>
            </div>
            <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
                <div className="space-y-6">
                    <div>
                        <h3 className="text-md font-medium text-gray-900 mb-2">Vitals</h3>
                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                            <div>
                                <label htmlFor="postHeartRate" className="block text-sm font-medium text-gray-700">Heart Rate (bpm)</label>
                                <input
                                    type="number"
                                    id="postHeartRate"
                                    name="postHeartRate"
                                    value={vitals.postHeartRate}
                                    onChange={handleVitalChange}
                                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                                />
                            </div>
                            <div>
                                <label htmlFor="postBloodPressure" className="block text-sm font-medium text-gray-700">Blood Pressure (mmHg)</label>
                                <input
                                    type="text"
                                    id="postBloodPressure"
                                    name="postBloodPressure"
                                    placeholder="e.g. 120/80"
                                    value={vitals.postBloodPressure}
                                    onChange={handleVitalChange}
                                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                                />
                            </div>
                        </div>
                    </div>

                    <div className="flex flex-col space-y-4">
                        <button
                            type="button"
                            onClick={onToggleAIScribe}
                            className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                        >
                            <FaMicrophone className="mr-2" /> Launch AI-Scribe
                        </button>

                        <button
                            type="button"
                            onClick={onSaveSession}
                            className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                        >
                            <FaSave className="mr-2" /> Save Session
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PostSessionAssessment;