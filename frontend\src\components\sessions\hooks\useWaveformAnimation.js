/**
 * Custom hook for managing real-time waveform animation in EECP displays.
 */

import { useEffect, useRef, useCallback } from 'react';
import { drawWaveform } from '../utils/canvasUtils';
import { ANIMATION_SPEED, COLORS } from '../constants/waveformConstants';

/**
 * Custom hook for managing real-time waveform animation.
 * Handles canvas animation loops, data scrolling, and rendering for multiple waveforms.
 *
 * @param {boolean} isRunning - Whether animation should be active
 * @param {Object} canvasRefs - Object containing canvas references
 * @param {Object} waveformData - Object containing waveform data arrays
 * @returns {Object} Animation control functions
 */
const useWaveformAnimation = (isRunning, canvasRefs, waveformData) => {
    const animationRef = useRef(null);
    const offsetRef = useRef({ ecg: 0, pleth: 0 });

    /**
     * Animation frame callback for continuous waveform rendering.
     * Updates both ECG and plethysmogram displays with scrolling effect.
     */
    const animate = useCallback(() => {
        const { ecgCanvasRef, plethCanvasRef } = canvasRefs;
        const { ecgData, plethData } = waveformData;

        if (!ecgCanvasRef?.current || !plethCanvasRef?.current ||
            !ecgData?.length || !plethData?.length) {
            return;
        }

        const ecgCanvas = ecgCanvasRef.current;
        const plethCanvas = plethCanvasRef.current;
        const ecgCtx = ecgCanvas.getContext('2d');
        const plethCtx = plethCanvas.getContext('2d');

        // Draw the ECG waveform with current offset
        drawWaveform(
            ecgCtx,
            ecgCanvas,
            ecgData,
            offsetRef.current.ecg,
            COLORS.ECG
        );

        // Draw the plethysmogram waveform with current offset
        drawWaveform(
            plethCtx,
            plethCanvas,
            plethData,
            offsetRef.current.pleth,
            COLORS.PLETHYSMOGRAM
        );

        // Update offsets for scrolling effect
        offsetRef.current.ecg = (offsetRef.current.ecg + ANIMATION_SPEED) % ecgData.length;
        offsetRef.current.pleth = (offsetRef.current.pleth + ANIMATION_SPEED) % plethData.length;

        // Request the next animation frame
        animationRef.current = requestAnimationFrame(animate);
    }, [canvasRefs, waveformData]);

    /**
     * Starts the animation loop for real-time waveform display.
     */
    const startAnimation = useCallback(() => {
        if (animationRef.current) return; // Already running

        animate();
    }, [animate]);

    /**
     * Stops the animation loop and cleans up resources.
     */
    const stopAnimation = useCallback(() => {
        if (animationRef.current) {
            cancelAnimationFrame(animationRef.current);
            animationRef.current = null;
        }
    }, []);

    /**
     * Resets animation offsets to starting position.
     */
    const resetAnimation = useCallback(() => {
        offsetRef.current = { ecg: 0, pleth: 0 };
    }, []);

    /**
     * Draws a single frame without starting continuous animation.
     * Useful for static displays or debugging.
     */
    const drawSingleFrame = useCallback(() => {
        const { ecgCanvasRef, plethCanvasRef } = canvasRefs;
        const { ecgData, plethData } = waveformData;

        if (!ecgCanvasRef?.current || !plethCanvasRef?.current ||
            !ecgData?.length || !plethData?.length) {
            return;
        }

        const ecgCanvas = ecgCanvasRef.current;
        const plethCanvas = plethCanvasRef.current;
        const ecgCtx = ecgCanvas.getContext('2d');
        const plethCtx = plethCanvas.getContext('2d');

        // Draw static waveforms at current offset
        drawWaveform(ecgCtx, ecgCanvas, ecgData, offsetRef.current.ecg, COLORS.ECG);
        drawWaveform(plethCtx, plethCanvas, plethData, offsetRef.current.pleth, COLORS.PLETHYSMOGRAM);
    }, [canvasRefs, waveformData]);

    // Handle animation start/stop based on isRunning prop
    useEffect(() => {
        if (isRunning) {
            startAnimation();
        } else {
            stopAnimation();
        }

        return () => {
            stopAnimation();
        };
    }, [isRunning, startAnimation, stopAnimation]);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            stopAnimation();
        };
    }, [stopAnimation]);

    return {
        startAnimation,
        stopAnimation,
        resetAnimation,
        drawSingleFrame,
        isAnimating: !!animationRef.current
    };
};

export default useWaveformAnimation;