{"name": "backend", "version": "1.0.0", "type": "module", "main": "dist/main.js", "scripts": {"dev": "nodemon --watch app --ext ts --exec \"ts-node -r tsconfig-paths/register app/main.ts\"", "build": "tsc", "start": "node dist/main.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/express": "^5.0.3", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.6", "@types/pdfmake": "^0.2.11", "dotenv": "^16.6.1", "jest": "^29.7.0", "nodemon": "^3.1.10", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tst": "^8.0.2", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "dependencies": {"@medplum/cli": "^4.2.0", "@medplum/core": "^4.1.12", "@medplum/fhirpath": "^0.9.6", "@medplum/mock": "^4.1.12", "@types/cors": "^2.8.19", "axios": "^1.10.0", "cors": "^2.8.5", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "tsx": "^4.1.0", "zod": "^3.25.67"}, "types": "./dist/main.d.ts", "description": ""}