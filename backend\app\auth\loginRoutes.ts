import { Router, Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { z } from 'zod';

import { LoginRequestSchema } from './authSchema';

//TODO: consider importing from helper/medplumHelper
import { MedplumService } from '../clients/medplum_client';

import { JWT_SECRET, JWT_ACCESS_TOKEN_EXPIRATION, JWT_REFRESH_TOKEN_EXPIRATION } from '../core/config';
import { validate } from "./authMiddleware";

const router = Router();

// Helper function to generate JWT tokens
const generateTokens = (user: {email: string }) => {
  const accessToken = jwt.sign(user, JWT_SECRET, { expiresIn: JWT_ACCESS_TOKEN_EXPIRATION });
  const refreshToken = jwt.sign(user, JWT_SECRET, { expiresIn: JWT_REFRESH_TOKEN_EXPIRATION });
  return { accessToken, refreshToken, expiresIn: JWT_ACCESS_TOKEN_EXPIRATION };
};

/**
 * POST /auth/login
 * Handles user login.
 */
router.post('/login', async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const { email, password } = req.body;

  try {
    // 1. Validate input credentials using Zod schema.
    LoginRequestSchema.parse(req.body);

    // 2. Call MedplumService login method
    const medplum = new MedplumService();
    const loginResp = await medplum.login(email, password);

    // 3. Ensure we have a valid token
    const payload = { userId: loginResp.profile?.id as string, email: loginResp.profile?.email as string};
    const {accessToken, refreshToken, expiresIn} = generateTokens(payload);

    // 4. Return tokens
    res.status(200).json({ accessToken, refreshToken, expiresIn });
  } catch (error: any) {
    console.error('Login failed:', error);
    res.status(error.status || 500).json({ error: error.message || 'Login failed.' });
  }
});

/**
 * POST /auth/refresh
 * Refreshes the access token using a refresh token.
 */
router.post('/refresh', async (req: Request, res: Response): Promise<void> => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    res.status(401).json({ error: 'Refresh token is required.' });
    return;
  }

  try {
    const user = jwt.verify(refreshToken, JWT_SECRET) as any;
    const { accessToken, refreshToken: newRefreshToken, expiresIn } = generateTokens(user);
    res.status(200).json({ accessToken, refreshToken: newRefreshToken, expiresIn });
  } catch (err: any) {
    console.log('JWT verification failed:', err.message);
    res.status(403).json({ error: 'Invalid or expired refresh token.' });
  }
});

export default router;