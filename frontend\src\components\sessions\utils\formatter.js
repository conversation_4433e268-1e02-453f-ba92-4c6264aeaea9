
/**
 *@file formatters.js
 * Utility functions for data formatting.
 */

/**
 * Formats seconds into MM:SS time format.
 * 
 * @param {number} seconds - Total seconds to format
 * @returns {string} Formatted time string
 */
export const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60).toString().padStart(2, '0');
    const secs = (seconds % 60).toString().padStart(2, '0');
    return `${mins}:${secs}`;
};

/**
 * Validates blood pressure format (e.g., "120/80").
 * 
 * @param {string} bloodPressure - Blood pressure string
 * @returns {boolean} True if valid format
 */
export const validateBloodPressure = (bloodPressure) => {
    const bpRegex = /^\d{2,3}\/\d{2,3}$/;
    return bpRegex.test(bloodPressure);
};

/**
 * Parses blood pressure string into systolic and diastolic values.
 * 
 * @param {string} bloodPressure - Blood pressure string (e.g., "120/80")
 * @returns {Object|null} Object with systolic and diastolic values or null if invalid
 */
export const parseBloodPressure = (bloodPressure) => {
    if (!validateBloodPressure(bloodPressure)) {
        return null;
    }

    const [systolic, diastolic] = bloodPressure.split('/').map(Number);
    return { systolic, diastolic };
};