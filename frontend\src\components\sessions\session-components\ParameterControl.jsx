/**
 * Reusable component for EECP parameter input controls with sliders and tooltips.
 */

import { FaInfoCircle } from 'react-icons/fa';

/**
 * Renders a parameter control with label, slider, value display, and optional tooltip.
 * Provides consistent styling and behavior for all EECP parameter inputs.
 *
 * @param {Object} props
 * @param {string} props.name - Parameter name for form input
 * @param {string} props.label - Display label for the parameter
 * @param {number} props.value - Current parameter value
 * @param {number} props.min - Minimum allowed value
 * @param {number} props.max - Maximum allowed value
 * @param {number} props.step - Step increment for slider
 * @param {string} props.unit - Unit of measurement (e.g., "mmHg", "ms")
 * @param {string} props.tooltip - Optional tooltip text for info icon
 * @param {Function} props.onChange - Callback for value changes
 * @param {number} props.precision - Number of decimal places for display
 */
const ParameterControl = ({
    name,
    label,
    value,
    min,
    max,
    step,
    unit = '',
    tooltip,
    onChange,
    precision = 0
}) => {
    /**
     * Formats the value for display based on precision setting.
     *
     * @param {number} val - Value to format
     * @returns {string} Formatted value string
     */
    const formatValue = (val) => {
        if (precision === 0) {
            return Math.round(val).toString();
        }
        return val.toFixed(precision);
    };

    /**
     * Handles input change events and calls parent onChange.
     *
     * @param {Event} e - Input change event
     */
    const handleChange = (e) => {
        if (onChange) {
            onChange(e);
        }
    };

    return (
        <div className="parameter-control">
            <label
                htmlFor={name}
                className="block text-xs font-medium text-gray-700 mb-1"
            >
                {label}
                {unit && ` (${unit})`}
                {tooltip && (
                    <span
                        className="ml-1 text-gray-500 cursor-help"
                        title={tooltip}
                    >
                        <FaInfoCircle className="inline h-3 w-3" />
                    </span>
                )}
            </label>

            <div className="flex items-center">
                <input
                    type="range"
                    id={name}
                    name={name}
                    min={min}
                    max={max}
                    step={step}
                    value={value}
                    onChange={handleChange}
                    className="flex-grow h-2 bg-gray-200 rounded-lg appearance-none 
                     cursor-pointer slider"
                />
                <span className="ml-2 text-sm font-medium text-gray-900 w-12 text-right">
                    {formatValue(value)}
                </span>
            </div>

            {/* Custom slider styling */}
            <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          height: 16px;
          width: 16px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: 2px solid white;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .slider::-moz-range-thumb {
          height: 16px;
          width: 16px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: 2px solid white;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .slider:focus {
          outline: none;
        }
        
        .slider:focus::-webkit-slider-thumb {
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        }
      `}</style>
        </div>
    );
};

export default ParameterControl;