/**
 * @file PreSessionForm.jsx
 * Component for pre-session patient validation and vital signs collection.
 */

import { WEIGHT_UNITS } from '../constants/sessionConstants';
/**
 * Renders the pre-session form for patient validation and vital signs.
 *
 * @param {Object} props
 * @param {Object} props.sessionData - Current session data
 * @param {string} props.patientId - Patient ID input value
 * @param {string} props.error - Error message to display
 * @param {string} props.success - Success message to display
 * @param {Function} props.onPatientIdChange - Callback for patient ID changes
 * @param {Function} props.onIdSubmit - Callback for form submission
 * @param {Function} props.onInputChange - Callback for input changes
 * @param {Function} props.onNestedInputChange - Callback for nested input changes
 * @param {Function} props.onPersonnelChange - Callback for personnel changes
 * @param {Function} props.onStartSession - Callback to start session
 */
const PreSessionForm = ({
    sessionData,
    patientId,
    error,
    success,
    onPatientIdChange,
    onIdSubmit,
    onInputChange,
    onNestedInputChange,
    onPersonnelChange,
    onStartSession
}) => {
    return (
        <div className="max-w-2xl mx-auto">
            <div className="text-center mb-12">
                <h1 className="text-3xl font-light text-gray-900 mb-3">Pre-session setup</h1>
                <p className="text-gray-500">Enter patient measurements to begin EECP treatment</p>
            </div>

            <div className="flex flex-col gap-4">
                <form onSubmit={onIdSubmit} className="flex flex-col gap-4">
                    <label htmlFor="patientId" className="font-medium">Patient ID</label>
                    <input
                        id="patientId"
                        type="text"
                        value={patientId}
                        onChange={(e) => onPatientIdChange(e.target.value)}
                        placeholder="e.g. 123 or SYN-964441"
                        className="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <button
                        type="submit"
                        disabled={!patientId || patientId.trim() === ''}
                        className="bg-black text-white px-4 py-2 rounded-md hover:bg-gray-800
                       disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-black"
                    >
                        Validate Patient
                    </button>
                </form>

                <div className="flex flex-col gap-4 mb-6">
                    {error && (
                        <div className="mt-4 bg-red-50 border-l-4 border-red-400 p-3 text-red-700">
                            {error}
                        </div>
                    )}
                    {success && (
                        <div className="mt-4 bg-green-50 border-l-4 border-green-400 p-3 text-green-700">
                            {success}
                        </div>
                    )}
                </div>
            </div>

            {/* Vital Signs*/}
            <div className="mb-8">
                <h2 className="text-lg font-medium text-gray-900 mb-6">Vital signs</h2>
                <div className="space-y-6">
                    {/* Weight Row */}
                    <div className="flex space-x-4">
                        <div className="flex-1">
                            <label className="block text-sm font-medium text-gray-700 mb-2">Weight</label>
                            <input
                                type="text"
                                inputMode="decimal"
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                                value={sessionData.measurements.preEECP.weight}
                                onChange={(e) => {
                                    const value = e.target.value;
                                    if (value === '' || /^\d*\.?\d*$/.test(value)) {
                                        onInputChange('preEECP', 'weight', value);
                                    }
                                }}
                                placeholder="185.5"
                            />
                        </div>
                        <div className="w-24">
                            <label className="block text-sm font-medium text-gray-700 mb-2">Unit</label>
                            <select
                                className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent bg-white"
                                value={sessionData.measurements.preEECP.weightUnit}
                                onChange={(e) => onInputChange('preEECP', 'weightUnit', e.target.value)}
                            >
                                {WEIGHT_UNITS.map(unit => (
                                    <option key={unit.value} value={unit.value}>
                                        {unit.label}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>

                    {/* Blood Pressure Row */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Blood pressure</label>
                        <div className="flex space-x-4">
                            <div className="flex-1">
                                <input
                                    type="text"
                                    inputMode="numeric"
                                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                                    value={sessionData.measurements.preEECP.bloodPressure.systolic}
                                    onChange={(e) => {
                                        const value = e.target.value;
                                        if (value === '' || /^\d+$/.test(value)) {
                                            onNestedInputChange('preEECP', 'bloodPressure', 'systolic', value);
                                        }
                                    }}
                                    placeholder="120"
                                />
                                <div className="text-xs text-gray-500 mt-1">Systolic</div>
                            </div>
                            <div className="flex items-center text-gray-400 pb-6">/</div>
                            <div className="flex-1">
                                <input
                                    type="text"
                                    inputMode="numeric"
                                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                                    value={sessionData.measurements.preEECP.bloodPressure.diastolic}
                                    onChange={(e) => {
                                        const value = e.target.value;
                                        if (value === '' || /^\d+$/.test(value)) {
                                            onNestedInputChange('preEECP', 'bloodPressure', 'diastolic', value);
                                        }
                                    }}
                                    placeholder="80"
                                />
                                <div className="text-xs text-gray-500 mt-1">Diastolic</div>
                            </div>
                        </div>
                    </div>

                    {/* Pulse and SpO2 Row */}
                    <div className="flex space-x-4">
                        <div className="flex-1">
                            <label className="block text-sm font-medium text-gray-700 mb-2">Pulse rate</label>
                            <input
                                type="text"
                                inputMode="numeric"
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                                value={sessionData.measurements.preEECP.pulseRate}
                                onChange={(e) => {
                                    const value = e.target.value;
                                    if (value === '' || /^\d+$/.test(value)) {
                                        onInputChange('preEECP', 'pulseRate', value);
                                    }
                                }}
                                placeholder="70"
                            />
                            <div className="text-xs text-gray-500 mt-1">BPM</div>
                        </div>
                        <div className="flex-1">
                            <label className="block text-sm font-medium text-gray-700 mb-2">SpO2</label>
                            <input
                                type="text"
                                inputMode="numeric"
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                                value={sessionData.measurements.preEECP.spo2}
                                onChange={(e) => {
                                    const value = e.target.value;
                                    if (value === '' || /^\d+$/.test(value)) {
                                        onInputChange('preEECP', 'spo2', value);
                                    }
                                }}
                                placeholder="98"
                            />
                            <div className="text-xs text-gray-500 mt-1">%</div>
                        </div>
                    </div>

                    {/* O2 Liters Row */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">O2 liters <span className="text-gray-400">(optional)</span></label>
                        <input
                            type="text"
                            inputMode="decimal"
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                            value={sessionData.measurements.preEECP.o2Liters || ''}
                            onChange={(e) => {
                                const value = e.target.value;
                                if (value === '' || /^\d*\.?\d*$/.test(value)) {
                                    onInputChange('preEECP', 'o2Liters', value);
                                }
                            }}
                            placeholder="2.0"
                        />
                        <div className="text-xs text-gray-500 mt-1">L/min</div>
                    </div>
                </div>
            </div>

            {/* Personnel */}
            <div className="mb-12">
                <h2 className="text-lg font-medium text-gray-900 mb-6">Personnel</h2>
                <div className="space-y-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Therapist</label>
                        <input
                            type="text"
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                            value={sessionData.personnel.therapist}
                            onChange={(e) => onPersonnelChange('therapist', e.target.value)}
                            placeholder="Emma Thompson, RN"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Physician</label>
                        <input
                            type="text"
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                            value={sessionData.personnel.physician}
                            onChange={(e) => onPersonnelChange('physician', e.target.value)}
                            placeholder="Dr. Michael Chen"
                        />
                    </div>
                </div>
            </div>

            <button
                onClick={onStartSession}
                disabled={!success} // Disable if patient not validated
                className="w-full bg-black hover:bg-gray-800 text-white font-medium py-4 px-6 rounded-lg transition-colors
                   disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-black"
                title={!success ? "Please validate patient first" : "Start EECP session"}
            >
                Start session
            </button>
        </div>
    );
};

export default PreSessionForm;