/**
 * @file SymptomsTab.jsx
 * Component for displaying and editing symptom assessments in post-treatment evaluation.
 * <PERSON>les angina symptoms, cardiovascular symptoms, and other related symptoms reporting.
 */


import { Heart, Zap, AlertTriangle, CheckCircle, Info, Brain } from 'lucide-react';
import { CARD_STYLES } from '../constants/commonStyles';
import { SYMPTOM_SCALES } from '../constants/assessmentConstants';
import {
  AssessmentSelect,
  SummaryCard,
  ClinicalNotes,
  SectionHeader
} from '../evaluation-components/commonComponents';
import { calculateSymptomBurden, getAnginaAssessment, getRecommendations } from '../utils/assessmentUtils';


/**
 * Status indicator component for symptom assessment
 */
const SymptomStatus = ({ value, scale, label, type = "severity" }) => {
  if (!value) return null;

  const option = scale.find(opt => opt.value === value);
  if (!option) return null;

  let status = '';
  let description = '';
  let colorClass = '';
  let Icon = Info;
  let recommendations = [];

  const score = option.score;

  if (score === 0) {
    status = 'No symptoms';
    description = 'Excellent - no reported symptoms';
    colorClass = 'text-green-600 bg-green-50 border-green-200';
    Icon = CheckCircle;
  } else if (score === 1) {
    status = 'Mild symptoms';
    description = 'Minimal impact on daily activities';
    colorClass = 'text-blue-600 bg-blue-50 border-blue-200';
    Icon = Info;
    if (type === "frequency") recommendations.push('Monitor symptom patterns');
  } else if (score === 2) {
    status = 'Moderate symptoms';
    description = 'Some limitation of activities';
    colorClass = 'text-yellow-600 bg-yellow-50 border-yellow-200';
    Icon = AlertTriangle;
    recommendations.push('Consider symptom management strategies');
  } else if (score >= 3) {
    status = 'Significant symptoms';
    description = 'Substantial impact on quality of life';
    colorClass = 'text-red-600 bg-red-50 border-red-200';
    Icon = AlertTriangle;
    recommendations.push('Requires medical attention');
    if (score >= 4) recommendations.push('Consider urgent evaluation');
  }

  // Specific recommendations by symptom type
  if (label.includes('Angina') && score >= 2) {
    recommendations.push('Optimize anti-anginal therapy');
  }
  if (label.includes('Shortness of Breath') && score >= 2) {
    recommendations.push('Evaluate heart failure management');
  }
  if (label.includes('Chest Pain') && score >= 3) {
    recommendations.push('Rule out acute coronary syndrome');
  }

  return (
    <div className={`p-3 rounded-lg border ${colorClass} mt-2`}>
      <div className="flex items-center mb-1">
        <Icon size={16} className="mr-2" />
        <div>
          <span className="text-sm font-medium">
            {label}: {status}
          </span>
          <div className="text-xs opacity-75">{description}</div>
        </div>
      </div>
      {recommendations.length > 0 && (
        <div className="mt-2">
          <ul className="text-xs opacity-75 space-y-1">
            {recommendations.map((rec, index) => (
              <li key={index}>• {rec}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

/**
 * Angina-specific assessment component
 */
const AnginaAssessment = ({ anginaFrequency, anginaSeverity }) => {
  const anginaData = getAnginaAssessment(anginaFrequency, anginaSeverity);

  if (!anginaData) return null;

  const { assessment, ccsClass, color, burden } = anginaData;

  return (
    <div className={`p-4 rounded-lg border ${color} mt-4`}>
      <div className="flex items-center mb-2">
        <Info size={20} className="mr-2" />
        <h5 className="font-medium">Angina Assessment: {assessment}</h5>
      </div>
      <div className="text-sm opacity-75 mb-2">{ccsClass}</div>
      {burden >= 2 && (
        <div className="text-xs opacity-75">
          <p><strong>Recommendations:</strong></p>
          <ul className="mt-1 space-y-1">
            <li>• Review current anti-anginal medications</li>
            <li>• Consider cardiology consultation</li>
            {burden >= 3 && <li>• Evaluate for revascularization</li>}
          </ul>
        </div>
      )}
    </div>
  );
};


/**
 * Symptom burden summary component
 */
const SymptomBurdenSummary = ({ symptoms }) => {
  const burdenData = calculateSymptomBurden(symptoms);

  if (burdenData.assessed === 0) {
    return (
      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
        <h4 className="text-lg font-medium text-gray-800 mb-2">Symptom Burden Summary</h4>
        <p className="text-gray-600">Complete symptom assessments to see burden analysis</p>
      </div>
    );
  }

  // Determine overall symptom burden status
  let overallStatus = '';
  let statusColor = '';
  let Icon = Info;

  if (burdenData.percentage === 0) {
    overallStatus = 'No Symptoms';
    statusColor = 'text-green-600';
    Icon = CheckCircle;
  } else if (burdenData.percentage <= 25) {
    overallStatus = 'Minimal Burden';
    statusColor = 'text-blue-600';
    Icon = CheckCircle;
  } else if (burdenData.percentage <= 50) {
    overallStatus = 'Moderate Burden';
    statusColor = 'text-yellow-600';
    Icon = AlertTriangle;
  } else if (burdenData.percentage <= 75) {
    overallStatus = 'High Burden';
    statusColor = 'text-orange-600';
    Icon = AlertTriangle;
  } else {
    overallStatus = 'Severe Burden';
    statusColor = 'text-red-600';
    Icon = AlertTriangle;
  }

  // Get recommendations from utility
  const recommendations = getRecommendations('symptoms', burdenData.percentage);

  return (
    <SummaryCard
      title="Symptom Burden Summary"
      icon={Icon}
      metrics={[
        {
          label: 'Symptom Burden',
          value: `${burdenData.percentage}%`,
          color: statusColor
        },
        {
          label: 'Symptoms Assessed',
          value: `${burdenData.assessed}/${burdenData.total}`,
          color: 'text-blue-600'
        },
        {
          label: overallStatus,
          value: '',
          color: statusColor
        }
      ]}
      recommendations={recommendations}
    />
  );
};



/**
 * Symptoms tab component for post-treatment evaluation
 */
const SymptomsTab = ({ symptoms, onChange }) => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <SectionHeader
        title="Symptoms Assessment"
        icon={Heart}
        iconColor="text-red-600"
      />

      {/* Angina Symptoms */}
      <div className={CARD_STYLES.SECTION}>
        <h4 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
          <Heart className="mr-2 text-red-500" size={20} />
          Angina Symptoms
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <AssessmentSelect
            label="Angina Frequency"
            value={symptoms.anginaFrequency}
            onChange={onChange}
            fieldName="anginaFrequency"
            options={SYMPTOM_SCALES.FREQUENCY}
            description="How often do you experience chest pain/angina?"
          />
          <AssessmentSelect
            label="Angina Severity"
            value={symptoms.anginaSeverity}
            onChange={onChange}
            fieldName="anginaSeverity"
            options={SYMPTOM_SCALES.SEVERITY}
            description="When you have angina, how severe is it?"
          />
        </div>

        {/* Angina-specific assessment */}
        <AnginaAssessment
          anginaFrequency={symptoms.anginaFrequency}
          anginaSeverity={symptoms.anginaSeverity}
        />
      </div>

      {/* Cardiovascular Symptoms */}
      <div className={CARD_STYLES.SECTION}>
        <h4 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
          <Zap className="mr-2 text-blue-500" size={20} />
          Cardiovascular Symptoms
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <AssessmentSelect
            label="Shortness of Breath"
            value={symptoms.shortnessOfBreath}
            onChange={onChange}
            fieldName="shortnessOfBreath"
            options={SYMPTOM_SCALES.SEVERITY}
            description="How severe is your shortness of breath?"
          />
          <AssessmentSelect
            label="Fatigue"
            value={symptoms.fatigue}
            onChange={onChange}
            fieldName="fatigue"
            options={SYMPTOM_SCALES.SEVERITY}
            description="How severe is your fatigue or tiredness?"
          />
          <AssessmentSelect
            label="Chest Pain (Non-Anginal)"
            value={symptoms.chestPain}
            onChange={onChange}
            fieldName="chestPain"
            options={SYMPTOM_SCALES.SEVERITY}
            description="Any other chest discomfort besides angina?"
          />
          <AssessmentSelect
            label="Palpitations"
            value={symptoms.palpitations}
            onChange={onChange}
            fieldName="palpitations"
            options={SYMPTOM_SCALES.SEVERITY}
            description="How severe are heart palpitations or irregular beats?"
          />
        </div>

        {/* Cardiovascular symptoms status */}
        <SymptomStatus value={symptoms.shortnessOfBreath} scale={SYMPTOM_SCALES.SEVERITY} label="Shortness of Breath" />
        <SymptomStatus value={symptoms.fatigue} scale={SYMPTOM_SCALES.SEVERITY} label="Fatigue" />
        <SymptomStatus value={symptoms.chestPain} scale={SYMPTOM_SCALES.SEVERITY} label="Chest Pain" />
        <SymptomStatus value={symptoms.palpitations} scale={SYMPTOM_SCALES.SEVERITY} label="Palpitations" />
      </div>

      {/* General Symptoms */}
      <div className={CARD_STYLES.SECTION}>
        <h4 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
          <Brain className="mr-2 text-purple-500" size={20} />
          General Symptoms
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <AssessmentSelect
            label="Dizziness"
            value={symptoms.dizziness}
            onChange={onChange}
            fieldName="dizziness"
            options={SYMPTOM_SCALES.SEVERITY}
            description="How severe is your dizziness or lightheadedness?"
          />
          <AssessmentSelect
            label="Headache"
            value={symptoms.headache}
            onChange={onChange}
            fieldName="headache"
            options={SYMPTOM_SCALES.SEVERITY}
            description="How severe are your headaches?"
          />
        </div>

        {/* General symptoms status */}
        <SymptomStatus value={symptoms.dizziness} scale={SYMPTOM_SCALES.SEVERITY} label="Dizziness" />
        <SymptomStatus value={symptoms.headache} scale={SYMPTOM_SCALES.SEVERITY} label="Headache" />
      </div>

      {/* Symptom Burden Summary */}
      <SymptomBurdenSummary symptoms={symptoms} />

      {/* Clinical Notes */}
      <ClinicalNotes
        value={symptoms.notes}
        onChange={onChange}
        placeholder="Enter clinical observations, symptom patterns, triggers, medication effects, or any relevant symptom information..."
      />
    </div>
  );
};

export default SymptomsTab;