import {Request, Response, NextFunction } from 'express';
import {Schem<PERSON>, z, ZodError} from 'zod';
import jwt from 'jsonwebtoken';
import {JWT_SECRET} from '../core/config';

// Extend the Request interface to include the user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        email: string;
        firstName?: string;
        lastName?: string;
        role?: 'admin' | 'doctor' | 'nurse';
      };
    }
  }
}

export const authenticationToken = (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer token
  if (token == null) {
    return res.status(401).json({error: "Authentication token required."});
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      // Token is invalid or expired
      console.log('JWT verification failed:', err.message);
      return res.status(403).json({error: "Invalid or expired authentication token."});
    }

    // Attach the decoded user payload to the request
    req.user = user as Request['user'];
    next();
  });
};

export const validate = (schema: z.AnyZodObject) =>
  (req: Request, res: Response, next: NextFunction) => {
    try {
      schema.parse(req.body);
      next();
    } catch (error: any) {
      if (error instanceof ZodError) {
        // Format Zod errors for better readability
        const errors = error.errors.map(err => ({
          path: err.path.join('.'),
          message: err.message,
          code: err.code,
        }));
        return res.status(400).json({
          error: "Validation failed.", 
          details: errors
        });
      }
      next(error);
    }
  };