/**
 * EvaluationDetailsModal.jsx
 * Modal component for displaying detailed post-treatment evaluation information.
 * Shows comprehensive evaluation data including vitals, functional tests, and quality of life metrics.
 */

import {
  X,
  FileText,
  Calendar,
  User,
  Heart,
  Activity,
  BarChart2,
  Star,
  TrendingUp
} from 'lucide-react';

// Constants
const STAR_RATING_MAX = 5;
const DATE_FORMAT_OPTIONS = {
  year: 'numeric',
  month: 'long',
  day: 'numeric',
  hour: '2-digit',
  minute: '2-digit'
};

const IMPROVEMENT_STATUS = {
  IMPROVED: 'Improved',
  STABLE: 'Stable',
  DECLINED: 'Declined'
};

/**
 * Formats a date string into a human-readable format.
 * Used for displaying evaluation dates in the modal header.
 * 
 * @param {string} dateString - ISO date string to format
 * @returns {string} Formatted date string in locale format
 */
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', DATE_FORMAT_OPTIONS);
};

/**
 * Renders a star rating component based on a numeric value.
 * Used for displaying quality of life metrics in visual star format.
 * 
 * @param {string|number} value - Rating value (0-5)
 * @returns {JSX.Element} Star rating component
 */
const renderStarRating = (value) => {
  const numericValue = parseInt(value || 0);

  return (
    <div className="flex">
      {[...Array(STAR_RATING_MAX)].map((_, index) => (
        <Star
          key={index}
          className={`h-4 w-4 ${index < numericValue
            ? 'text-yellow-400 fill-yellow-400'
            : 'text-gray-300'
            }`}
        />
      ))}
    </div>
  );
};

/**
 * Renders the modal header section with title and close button.
 * 
 * @param {Function} onClose - Callback function to close the modal
 * @returns {JSX.Element} Modal header component
 */
const renderModalHeader = (onClose) => (
  <div className="flex justify-between items-center mb-6">
    <div className="flex items-center">
      <FileText className="h-6 w-6 text-blue-600 mr-2" />
      <h3 className="text-lg font-medium text-gray-900">
        Post-Treatment Evaluation Details
      </h3>
    </div>
    <button
      onClick={onClose}
      className="text-gray-400 hover:text-gray-500"
      aria-label="Close modal"
    >
      <X className="h-6 w-6" />
    </button>
  </div>
);

/**
 * Renders the evaluation basic information section.
 * Shows evaluation date, author, and summary information.
 * 
 * @param {Object} evaluation - Evaluation data object
 * @returns {JSX.Element} Basic information section
 */
const renderBasicInformation = (evaluation) => (
  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
    {/* Basic Info */}
    <div className="bg-gray-50 rounded-lg p-4">
      <h4 className="text-sm font-medium text-gray-500 mb-3">Evaluation Information</h4>
      <div className="space-y-2">
        <div className="flex items-center">
          <Calendar className="h-4 w-4 text-gray-400 mr-2" />
          <span className="text-sm text-gray-600">
            Date: {formatDate(evaluation.date)}
          </span>
        </div>
        <div className="flex items-center">
          <User className="h-4 w-4 text-gray-400 mr-2" />
          <span className="text-sm text-gray-600">
            Evaluated by: {evaluation.author}
          </span>
        </div>
      </div>
    </div>

    {/* Summary */}
    <div className="bg-gray-50 rounded-lg p-4">
      <h4 className="text-sm font-medium text-gray-500 mb-3">Summary</h4>
      <p className="text-sm text-gray-600">{evaluation.summary}</p>
    </div>
  </div>
);

/**
 * Renders the vitals section with blood pressure, heart rate, and BMI.
 * Displays key vital signs measured during the evaluation.
 * 
 * @param {Object} vitalsData - Vital signs data from evaluation
 * @returns {JSX.Element} Vitals section component
 */
const renderVitalsSection = (vitalsData) => (
  <div className="mt-6">
    <h4 className="text-sm font-medium text-gray-500 mb-3">Vitals</h4>
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div className="bg-white border rounded-lg p-4">
        <div className="flex items-center">
          <Activity className="h-4 w-4 text-blue-600 mr-2" />
          <span className="text-sm font-medium text-gray-900">Blood Pressure</span>
        </div>
        <div className="mt-2 text-2xl font-semibold text-gray-900">
          {vitalsData.systolic || '--'}/{vitalsData.diastolic || '--'} mmHg
        </div>
      </div>
      <div className="bg-white border rounded-lg p-4">
        <div className="flex items-center">
          <Heart className="h-4 w-4 text-red-600 mr-2" />
          <span className="text-sm font-medium text-gray-900">Heart Rate</span>
        </div>
        <div className="mt-2 text-2xl font-semibold text-gray-900">
          {vitalsData.heartRate || '--'} bpm
        </div>
      </div>
      <div className="bg-white border rounded-lg p-4">
        <div className="flex items-center">
          <BarChart2 className="h-4 w-4 text-green-600 mr-2" />
          <span className="text-sm font-medium text-gray-900">BMI</span>
        </div>
        <div className="mt-2 text-2xl font-semibold text-gray-900">
          {vitalsData.bmi || '--'}
        </div>
      </div>
    </div>
  </div>
);

/**
 * Renders the functional tests section with CCS class and LVEF.
 * Shows cardiac functional assessment results with improvement indicators.
 * 
 * @param {Object} functionalTestsData - Functional test data from evaluation
 * @returns {JSX.Element} Functional tests section component
 */
const renderFunctionalTestsSection = (functionalTestsData) => (
  <div className="mt-6">
    <h4 className="text-sm font-medium text-gray-500 mb-3">Functional Tests</h4>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="bg-white border rounded-lg p-4">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-900">CCS Angina Class</span>
          <div className="flex items-center">
            <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
            <span className="text-sm text-green-600">{IMPROVEMENT_STATUS.IMPROVED}</span>
          </div>
        </div>
        <div className="mt-2 text-2xl font-semibold text-gray-900">
          Class {functionalTestsData.ccsClass || '--'}
        </div>
      </div>
      <div className="bg-white border rounded-lg p-4">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-900">LVEF</span>
          <div className="flex items-center">
            <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
            <span className="text-sm text-green-600">{IMPROVEMENT_STATUS.IMPROVED}</span>
          </div>
        </div>
        <div className="mt-2 text-2xl font-semibold text-gray-900">
          {functionalTestsData.lvef || '--'}%
        </div>
      </div>
    </div>
  </div>
);

/**
 * Renders the quality of life section with star ratings.
 * Displays patient-reported outcomes using visual star rating system.
 * 
 * @param {Object} qualityOfLifeData - Quality of life data from evaluation
 * @returns {JSX.Element} Quality of life section component
 */
const renderQualityOfLifeSection = (qualityOfLifeData) => (
  <div className="mt-6">
    <h4 className="text-sm font-medium text-gray-500 mb-3">Quality of Life</h4>
    <div className="bg-white border rounded-lg p-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-900">Energy Level</span>
            {renderStarRating(qualityOfLifeData.energyLevel)}
          </div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-900">Sleep Quality</span>
            {renderStarRating(qualityOfLifeData.sleepQuality)}
          </div>
        </div>
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-900">Mood</span>
            {renderStarRating(qualityOfLifeData.mood)}
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-900">Social Activity</span>
            {renderStarRating(qualityOfLifeData.socialActivity)}
          </div>
        </div>
      </div>
    </div>
  </div>
);

/**
 * Renders the notes section if notes are available.
 * Shows additional comments or observations from the evaluation.
 * 
 * @param {string} notes - Notes text from evaluation
 * @returns {JSX.Element|null} Notes section component or null if no notes
 */
const renderNotesSection = (notes) => {
  if (!notes) return null;

  return (
    <div className="mt-6">
      <h4 className="text-sm font-medium text-gray-500 mb-3">Notes</h4>
      <div className="bg-white border rounded-lg p-4">
        <p className="text-sm text-gray-600">{notes}</p>
      </div>
    </div>
  );
};

/**
 * Renders the modal footer with close button.
 * 
 * @param {Function} onClose - Callback function to close the modal
 * @returns {JSX.Element} Modal footer component
 */
const renderModalFooter = (onClose) => (
  <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
    <button
      type="button"
      onClick={onClose}
      className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
    >
      Close
    </button>
  </div>
);

/**
 * Modal component for displaying detailed post-treatment evaluation information.
 * Provides a comprehensive view of patient evaluation data including vitals,
 * functional tests, quality of life metrics, and clinical notes.
 * 
 * @param {Object} props - Component props
 * @param {Object|null} props.evaluation - Evaluation data object to display
 * @param {Function} props.onClose - Callback function to close the modal
 * @returns {JSX.Element|null} Modal component or null if no evaluation data
 */
const EvaluationDetailsModal = ({ evaluation, onClose }) => {
  // Early return if no evaluation data provided
  if (!evaluation) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            {/* Header Section */}
            {renderModalHeader(onClose)}

            {/* Basic Information Section */}
            {renderBasicInformation(evaluation)}

            {/* Vitals Section */}
            {renderVitalsSection(evaluation.data.vitals)}

            {/* Functional Tests Section */}
            {renderFunctionalTestsSection(evaluation.data.functionalTests)}

            {/* Quality of Life Section */}
            {renderQualityOfLifeSection(evaluation.data.qualityOfLife)}

            {/* Notes Section (conditional) */}
            {renderNotesSection(evaluation.data.vitals.notes)}
          </div>

          {/* Footer Section */}
          {renderModalFooter(onClose)}
        </div>
      </div>
    </div>
  );
};

export default EvaluationDetailsModal;