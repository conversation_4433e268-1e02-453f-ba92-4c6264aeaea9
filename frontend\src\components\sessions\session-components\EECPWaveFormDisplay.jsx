/**
 * Main component for displaying real-time ECG and plethysmogram waveforms.
 * Refactored for modularity and maintainability.
 */

import React, { useState, useEffect, useRef } from 'react';

// Hooks
import useAutoSave from '../hooks/useAutoSave';
import useWaveformAnimation from '../hooks/useWaveformAnimation';

// Components
import WaveformCanvas from './WaveformCanvas';
import WaveformSettings from './WaveformSettings';
import WaveformActionButtons from './WaveformActionButtons';

// Utils
import { generateECGData, generatePlethData, applyParametersToECG, applyParametersToPleth } from '../utils/waveformGenerator';
import { createCompositeSnapshot } from '../utils/canvasUtils';

// Constants
import { DEFAULT_PARAMETERS } from '../constants/waveformConstants';

/**
 * Main EECP waveform display component with modular architecture.
 * Combines real-time animation, parameter controls, and auto-save functionality.
 *
 * @param {Object} props
 * @param {boolean} props.isRunning - Whether the waveform animation is running
 * @param {Object} props.sessionData - Current session data containing parameters
 * @param {Function} props.onParameterChange - Callback for parameter changes
 * @param {Function} props.onCaptureSnapshot - Callback for capturing snapshots
 */
const EECPWaveformDisplay = ({
    isRunning,
    sessionData,
    onParameterChange,
    onCaptureSnapshot
}) => {
    // Canvas references
    const ecgCanvasRef = useRef(null);
    const plethCanvasRef = useRef(null);

    // Component state
    const [showSettings, setShowSettings] = useState(false);
    const [parameters, setParameters] = useState({
        ...DEFAULT_PARAMETERS,
        ...sessionData
    });

    // Waveform data with parameter effects applied
    const [waveformData, setWaveformData] = useState({
        ecgData: [],
        plethData: []
    });

    // Custom hooks
    const autoSave = useAutoSave(
        isRunning,
        async (data) => {
            // In production, this would call an API
            console.log('Auto-saving session data:', data);
        },
        { parameters, sessionData }
    );

    const animation = useWaveformAnimation(
        isRunning,
        { ecgCanvasRef, plethCanvasRef },
        waveformData
    );

    /**
     * Handles parameter input changes and updates state.
     *
     * @param {Event} e - Input change event
     */
    const handleParameterChange = (e) => {
        const { name, value } = e.target;

        const newParameters = {
            ...parameters,
            [name]: parseFloat(value)
        };

        setParameters(newParameters);

        // Call parent callback if provided
        if (onParameterChange) {
            onParameterChange(newParameters);
        }
    };

    /**
     * Handles snapshot capture with composite image creation.
     */
    const handleCaptureSnapshot = () => {
        if (!ecgCanvasRef.current || !plethCanvasRef.current) return;

        const dataUrl = createCompositeSnapshot(
            ecgCanvasRef.current,
            plethCanvasRef.current
        );

        if (onCaptureSnapshot) {
            onCaptureSnapshot(dataUrl);
        }
    };

    /**
     * Callback for when canvas components are ready.
     *
     * @param {'ecg'|'pleth'} type - Canvas type
     * @param {React.RefObject} canvasRef - Canvas reference
     */
    const handleCanvasReady = (type) => (canvasRef) => {
        if (type === 'ecg') {
            ecgCanvasRef.current = canvasRef.current;
        } else if (type === 'pleth') {
            plethCanvasRef.current = canvasRef.current;
        }
    };

    // Generate and update waveform data when parameters change
    useEffect(() => {
        const baseECGData = generateECGData();
        const basePlethData = generatePlethData();

        // Apply parameter effects to waveforms
        const modifiedECGData = applyParametersToECG(baseECGData, parameters);
        const modifiedPlethData = applyParametersToPleth(basePlethData, parameters);

        setWaveformData({
            ecgData: modifiedECGData,
            plethData: modifiedPlethData
        });
    }, [parameters]);

    return (
        <div className="bg-white p-4 rounded-lg shadow">
            {/* Header section */}
            <div className="flex justify-between items-center mb-4">
                <div>
                    <h3 className="text-lg font-medium text-gray-900">
                        Real-time Waveforms
                    </h3>
                    <p className="text-sm text-gray-500">
                        ECG and Plethysmogram
                    </p>
                </div>

                {/* Action buttons */}
                <WaveformActionButtons
                    lastSaved={autoSave.lastSaved}
                    isSaving={autoSave.isSaving}
                    showSettings={showSettings}
                    onManualSave={autoSave.handleManualSave}
                    onCaptureSnapshot={handleCaptureSnapshot}
                    onToggleSettings={() => setShowSettings(!showSettings)}
                />
            </div>

            {/* Waveform displays */}
            <div className="grid grid-cols-1 gap-4">
                {/* ECG Waveform */}
                <WaveformCanvas
                    type="ecg"
                    label="ECG"
                    color="bg-red-500"
                    onCanvasReady={handleCanvasReady('ecg')}
                />

                {/* Plethysmogram Waveform */}
                <WaveformCanvas
                    type="plethysmogram"
                    label="Plethysmogram"
                    color="bg-green-500"
                    onCanvasReady={handleCanvasReady('pleth')}
                />
            </div>

            {/* Settings panel */}
            <WaveformSettings
                parameters={parameters}
                onParameterChange={handleParameterChange}
                isVisible={showSettings}
            />
        </div>
    );
};

export default EECPWaveformDisplay;