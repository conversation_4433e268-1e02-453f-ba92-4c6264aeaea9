/**
 *@file commonStyles.js
 * Shared styling constants for medical evaluation components.
 */

// Standard input field styles used across all evaluation components
export const INPUT_STYLES = {
  FIELD:
    "w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",
  SELECT:
    "w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white",
  TEXTAREA:
    "w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors h-24 resize-none",
  LABEL: "block text-sm font-medium text-gray-700 mb-2",
};

// Card and section styles for consistent layout
export const CARD_STYLES = {
  SECTION: "bg-gray-50 p-4 rounded-lg border border-gray-200",
  NOTES: "bg-blue-50 p-4 rounded-lg border border-blue-200",
  SUMMARY: "bg-blue-50 p-4 rounded-lg border border-blue-200",
};

// Status indicator color classes for different severity levels
export const STATUS_COLORS = {
  EXCELLENT: "text-green-600 bg-green-50 border-green-200",
  GOOD: "text-blue-600 bg-blue-50 border-blue-200",
  MODERATE: "text-yellow-600 bg-yellow-50 border-yellow-200",
  POOR: "text-orange-600 bg-orange-50 border-orange-200",
  SEVERE: "text-red-600 bg-red-50 border-red-200",
  NORMAL: "text-green-600 bg-green-50 border-green-200",
  ELEVATED: "text-red-600 bg-red-50 border-red-200",
  LOW: "text-blue-600 bg-blue-50 border-blue-200",
};

export const VITALS_BARCHART_OPTIONS = {
  responsive: true,
  plugins: {
    legend: {
      position: "top",
    },
    title: {
      display: true,
      text: "Vitals Comparison",
    },
  },
};

export const VITALS_CHART_PROPERTIES = {
  before: {
    label: "Before",
    backgroundColor: "rgba(75, 192, 192, 0.5)",
  },
  after: {
    label: "After",
    backgroundColor: "rgba(255, 99, 132, 0.5)",
  },
};
