/*
 * Utility functions for generating simulated ECG and plethysmogram waveform data.
 */

import { WAVEFORM_CYCLES, POINTS_PER_CYCLE } from '../constants/waveformConstants';

/**
 * Generates simulated ECG waveform data with P waves, QRS complex, and T waves.
 * Creates a realistic cardiac rhythm pattern for display purposes.
 *
 * @returns {Array<number>} Array of ECG amplitude values normalized between -1 and 1
 */
export const generateECGData = () => {
    const data = [];

    for (let cycle = 0; cycle < WAVEFORM_CYCLES; cycle++) {
        for (let i = 0; i < POINTS_PER_CYCLE; i++) {
            const x = i / POINTS_PER_CYCLE;
            let y = 0;

            // P wave generation (atrial depolarization)
            if (x < 0.2) {
                y = Math.sin(x * Math.PI * 5) * 0.2;
            }

            // QRS complex generation (ventricular depolarization)
            if (x >= 0.2 && x < 0.35) {
                const qrsX = (x - 0.2) / 0.15;
                y = Math.sin(qrsX * Math.PI) * (qrsX < 0.5 ? -0.8 : 1.2);
            }

            // T wave generation (ventricular repolarization)
            if (x >= 0.45 && x < 0.7) {
                y = Math.sin((x - 0.45) * Math.PI * 4) * 0.3;
            }

            data.push(y);
        }
    }

    return data;
};

/**
 * Generates simulated plethysmogram data with systolic peaks and diastolic decay.
 * Creates arterial pulse waveform patterns typical of EECP monitoring.
 *
 * @returns {Array<number>} Array of plethysmogram amplitude values normalized between 0 and 1
 */
export const generatePlethData = () => {
    const data = [];

    for (let cycle = 0; cycle < WAVEFORM_CYCLES; cycle++) {
        for (let i = 0; i < POINTS_PER_CYCLE; i++) {
            const x = i / POINTS_PER_CYCLE;
            let y = 0;

            // Systolic peak generation (arterial pulse upstroke)
            if (x < 0.3) {
                y = Math.pow(Math.sin(x * Math.PI / 0.3), 2) * 0.8;
            }

            // Diastolic decay generation (arterial pulse downstroke)
            if (x >= 0.3) {
                y = 0.8 * Math.exp(-(x - 0.3) * 5);
            }

            // Add dicrotic notch (aortic valve closure artifact)
            if (x >= 0.4 && x < 0.5) {
                y += Math.sin((x - 0.4) * Math.PI * 10) * 0.1;
            }

            data.push(y);
        }
    }

    return data;
};

/**
 * Adds realistic noise to waveform data for more authentic simulation.
 *
 * @param {Array<number>} data - Original waveform data
 * @param {number} noiseLevel - Noise amplitude (0-1)
 * @returns {Array<number>} Waveform data with added noise
 */
export const addNoiseToWaveform = (data, noiseLevel = 0.05) => {
    return data.map(value => {
        const noise = (Math.random() - 0.5) * 2 * noiseLevel;
        return value + noise;
    });
};

/**
 * Applies parameter-based modifications to ECG data.
 * Simulates how EECP parameters might affect cardiac rhythm.
 *
 * @param {Array<number>} ecgData - Original ECG data
 * @param {Object} parameters - EECP parameters
 * @returns {Array<number>} Modified ECG data
 */
export const applyParametersToECG = (ecgData, parameters) => {
    const { cuffPressure, inflationRatio, triggerDelay } = parameters;

    // Simple simulation: higher cuff pressure might slightly affect amplitude
    const pressureEffect = 1 + (cuffPressure - 220) / 1000;

    return ecgData.map(value => value * pressureEffect);
};

/**
 * Applies parameter-based modifications to plethysmogram data.
 * Simulates how EECP parameters affect arterial pulse characteristics.
 *
 * @param {Array<number>} plethData - Original plethysmogram data
 * @param {Object} parameters - EECP parameters
 * @returns {Array<number>} Modified plethysmogram data
 */
export const applyParametersToPleth = (plethData, parameters) => {
    const { cuffPressure, inflationRatio } = parameters;

    // Simulate enhanced diastolic augmentation with higher inflation ratio
    const augmentationEffect = 1 + (inflationRatio - 1.0) * 0.3;

    return plethData.map((value, index) => {
        // Apply augmentation primarily during diastolic phase
        const cyclePosition = (index % POINTS_PER_CYCLE) / POINTS_PER_CYCLE;
        if (cyclePosition > 0.3 && cyclePosition < 0.8) {
            return value * augmentationEffect;
        }
        return value;
    });
};