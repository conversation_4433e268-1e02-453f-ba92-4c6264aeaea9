/**
 * Component for waveform display action buttons (save, snapshot, settings).
 */
import { FaCog, FaSave, FaDownload } from 'react-icons/fa';

/**
 * Renders action buttons for waveform display controls.
 * Includes auto-save status, manual save, snapshot capture, and settings toggle.
 *
 * @param {Object} props
 * @param {string} props.lastSaved - Last saved timestamp text
 * @param {boolean} props.isSaving - Whether save operation is in progress
 * @param {boolean} props.showSettings - Whether settings panel is visible
 * @param {Function} props.onManualSave - Callback for manual save button
 * @param {Function} props.onCaptureSnapshot - Callback for snapshot button
 * @param {Function} props.onToggleSettings - Callback for settings toggle
 */
const WaveformActionButtons = ({
    lastSaved,
    isSaving,
    showSettings,
    onManualSave,
    onCaptureSnapshot,
    onToggleSettings
}) => {
    return (
        <div className="flex items-center space-x-2">
            {/* Auto-save status indicator */}
            <div className="text-xs text-gray-500 flex items-center">
                <span
                    className={`inline-block h-2 w-2 rounded-full mr-1 ${isSaving ? 'bg-yellow-500 animate-pulse' : 'bg-green-500'
                        }`}
                />
                {isSaving ? 'Saving...' : `Auto-saved ${lastSaved}`}
            </div>

            {/* Manual save button */}
            <button
                type="button"
                onClick={onManualSave}
                disabled={isSaving}
                className="inline-flex items-center p-1 border border-transparent 
                   rounded-full shadow-sm text-white bg-primary 
                   hover:bg-blue-700 focus:outline-none focus:ring-2 
                   focus:ring-offset-2 focus:ring-primary
                   disabled:opacity-50 disabled:cursor-not-allowed"
                title={isSaving ? 'Saving...' : 'Save now'}
            >
                <FaSave className={`h-4 w-4 ${isSaving ? 'animate-spin' : ''}`} />
            </button>

            {/* Snapshot capture button */}
            <button
                type="button"
                onClick={onCaptureSnapshot}
                className="inline-flex items-center p-1 border border-transparent 
                   rounded-full shadow-sm text-white bg-primary 
                   hover:bg-blue-700 focus:outline-none focus:ring-2 
                   focus:ring-offset-2 focus:ring-primary"
                title="Capture snapshot"
            >
                <FaDownload className="h-4 w-4" />
            </button>

            {/* Settings toggle button */}
            <button
                type="button"
                onClick={onToggleSettings}
                className={`inline-flex items-center p-1 border border-transparent 
                   rounded-full shadow-sm text-white 
                   focus:outline-none focus:ring-2 focus:ring-offset-2 
                   focus:ring-primary transition-colors ${showSettings
                        ? 'bg-blue-800 hover:bg-blue-900'
                        : 'bg-primary hover:bg-blue-700'
                    }`}
                title="Settings"
            >
                <FaCog className={`h-4 w-4 ${showSettings ? 'rotate-45' : ''} transition-transform`} />
            </button>
        </div>
    );
};

export default WaveformActionButtons;