/**
 * @file sessionUtils.js
 * Utility functions for session-related calculations and operations
 */

import { SESSION_STATUS, DEFAULT_TOTAL_SESSIONS } from '../constants/postTreatmentConstants';

/**
 * Calculates session completion statistics from patient session data.
 * Determines eligibility for post-treatment evaluation based on completion status.
 * 
 * @param {Object} patientData - Patient object containing session data
 * @returns {Object} Session completion statistics
 */
export const calculateSessionCompletion = (patientData) => {
    if (!patientData || !patientData.sessions) {
        return {
            completed: 0,
            total: DEFAULT_TOTAL_SESSIONS,
            isEligibleForEvaluation: false
        };
    }

    const sessions = patientData.sessions;
    const completedSessions = sessions.filter(session =>
        session.status === SESSION_STATUS.COMPLETED
    ).length;

    // Determine total sessions from patient plan or use default
    const totalSessions = patientData.treatmentPlan?.totalSessions ||
        patientData.totalSessions ||
        DEFAULT_TOTAL_SESSIONS;

    // Patient is eligible for evaluation if they've completed at least 90% of sessions
    const completionRate = completedSessions / totalSessions;
    const isEligibleForEvaluation = completionRate >= 0.9;

    return {
        completed: completedSessions,
        total: totalSessions,
        isEligibleForEvaluation,
        completionRate: Math.round(completionRate * 100)
    };
};

/**
 * Gets the latest completed session date for display purposes.
 * 
 * @param {Object} patientData - Patient object containing session data
 * @returns {string|null} Formatted date string or null if no completed sessions
 */
export const getLatestSessionDate = (patientData) => {
    if (!patientData || !patientData.sessions) {
        return null;
    }

    const completedSessions = patientData.sessions
        .filter(session => session.status === SESSION_STATUS.COMPLETED)
        .sort((a, b) => new Date(b.date) - new Date(a.date));

    if (completedSessions.length === 0) {
        return null;
    }

    return new Date(completedSessions[0].date).toLocaleDateString();
};

/**
 * Checks if patient has completed their treatment course.
 * Used to determine evaluation availability and messaging.
 * 
 * @param {Object} sessionStats - Session completion statistics
 * @returns {boolean} True if treatment is complete
 */
export const isTreatmentComplete = (sessionStats) => {
    return sessionStats.completed >= sessionStats.total;
};

/**
 * Gets session completion percentage for display
 * 
 * @param {Object} sessionStats - Session completion statistics
 * @returns {number} Completion percentage (0-100)
 */
export const getSessionCompletionPercentage = (sessionStats) => {
    if (!sessionStats || sessionStats.total === 0) {
        return 0;
    }
    return Math.round((sessionStats.completed / sessionStats.total) * 100);
};

/**
 * Determines if patient is eligible for evaluation based on completion threshold
 * 
 * @param {Object} sessionStats - Session completion statistics
 * @param {number} threshold - Minimum completion threshold (default: 0.9 for 90%)
 * @returns {boolean} True if eligible for evaluation
 */
export const isEligibleForEvaluation = (sessionStats, threshold = 0.9) => {
    if (!sessionStats || sessionStats.total === 0) {
        return false;
    }
    return (sessionStats.completed / sessionStats.total) >= threshold;
};