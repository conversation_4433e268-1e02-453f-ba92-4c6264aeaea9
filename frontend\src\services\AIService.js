/**
 * Service for AI-powered transcription and clinical note generation.
 * Handles OpenAI API integration for SOAP note creation.
 */

const API_ENDPOINTS = {
    WHISPER: 'https://api.openai.com/v1/audio/transcriptions',
    GPT4: 'https://api.openai.com/v1/chat/completions'
};

/**
 * Transcribes audio using OpenAI Whisper API.
 * 
 * @param {Blob} audioBlob - Audio data to transcribe
 * @returns {Promise<string>} Transcribed text
 */
export const transcribeAudio = async (audioBlob) => {
    const formData = new FormData();
    formData.append('file', audioBlob, 'recording.wav');
    formData.append('model', 'whisper-1');

    const apiKey = process.env.REACT_APP_OPENAI_API_KEY;
    const response = await fetch(API_ENDPOINTS.WHISPER, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${apiKey}`
        },
        body: formData
    });


    if (!response.ok) {
        throw new Error(`Whisper API error: ${response.status}`);
    }

    const data = await response.json();
    return data.text;
};

/**
 * Generates structured SOAP note from transcription.
 * 
 * @param {string} transcriptionText - Transcribed text
 * @returns {Promise<Object>} Structured SOAP note
 */
export const generateSOAPNote = async (transcriptionText) => {
    const apiKey = process.env.REACT_APP_OPENAI_API_KEY;

    const response = await fetch(API_ENDPOINTS.GPT4, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            model: "gpt-4",
            messages: [
                {
                    role: "system",
                    content: `You are a medical scribe assistant. Convert the following transcription from an EECP therapy session into a structured SOAP note.
                    Include relevant patient data and session information.
                    Format your response as a JSON object with the following structure:
                    {
                      "subjective": "Patient's reported symptoms and feelings",
                      "objective": "Measurable clinical data including vitals and observations",
                      "assessment": "Clinical assessment of patient's condition and response to therapy",
                      "plan": "Treatment plan and recommendations"
                    }
                    
                    IMPORTANT: Your entire response must be valid JSON that can be parsed with JSON.parse().`
                },
                {
                    role: "user",
                    content: transcriptionText
                }
            ],
            temperature: 0.3
        })
    });

    if (!response.ok) {
        console.error('GPT-4 API error:', response.status);
        const errorText = await response.text();
        console.error('Error details:', errorText);
        throw new Error(`API error in chat completion: ${response.status} ${errorText}`);
    }

    const soapData = await response.json();
    console.log('GPT-4 API response:', soapData);
    // Check if the response has the expected structure
    if (!soapData || !soapData.choices || !soapData.choices[0] || !soapData.choices[0].message || !soapData.choices[0].message.content) {
        console.error('Unexpected response format:', soapData);
        throw new Error('Unexpected response format from OpenAI API');
    }
    const contentStr = soapData.choices[0].message.content;
    return JSON.parse(contentStr);
};

/**
 * Generates simulated clinical note for demo purposes.
 * Creates dynamic SOAP notes based on current session and patient data.
 * Maintains exact same functionality as original simulateWhisperResponse.
 * 
 * @param {Object} patient - Patient data
 * @param {Object} session - Session data  
 * @param {Object} vitals - Vital signs data
 * @returns {Object} Generated transcription, SOAP note, and vital updates
 */
export const generateSimulatedNote = async (patient, session, vitals) => {
    // Simulate API processing delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Extract actual vital signs data that has been entered
    const actualBloodPressure = vitals.preBloodPressure || '120/80';
    const actualHeartRate = vitals.preHeartRate || '72';
    const actualOxygenSaturation = vitals.preOxygenSaturation || '98';

    // Generate dynamic clinical values
    const painScaleStart = 5 + Math.floor(Math.random() * 5); // 5-9
    const painScaleEnd = Math.max(1, painScaleStart - (2 + Math.floor(Math.random() * 3))); // Reduction by 2-4 points
    const cuffPressure = 220 + Math.floor(Math.random() * 60); // 220-280 mmHg
    const sessionDuration = 55 + Math.floor(Math.random() * 10); // 55-65 minutes

    // Generate symptoms improvement based on session progression
    const symptomsList = [];
    if (session && session.sessionNumber > 5) {
        symptomsList.push("improved exercise tolerance");
    }
    if (session && session.sessionNumber > 10) {
        symptomsList.push("reduced frequency of angina episodes");
    }
    if (session && session.sessionNumber > 15) {
        symptomsList.push("improved quality of life");
    }

    // Generate rare side effects
    const sideEffects = [];
    if (Math.random() > 0.9) {
        sideEffects.push("mild skin irritation at cuff sites");
    }
    if (Math.random() > 0.95) {
        sideEffects.push("slight leg discomfort during inflation");
    }

    // Generate comprehensive transcription text
    let transcriptionText = `Patient ${patient ? patient.name : 'John Doe'} is here for EECP session number ${session ? session.sessionNumber : 1} of ${session ? session.totalSessions : 35}. `;

    // Add subjective information
    transcriptionText += `Patient reports ${symptomsList.length > 0 ? symptomsList.join(" and ") + " since starting therapy. " : ""}`;
    transcriptionText += `Chest pain has decreased from a ${painScaleStart} to a ${painScaleEnd} on the pain scale. `;

    if (Math.random() > 0.5) {
        transcriptionText += "No shortness of breath during normal activities. ";
    } else {
        transcriptionText += "Mild shortness of breath only with significant exertion. ";
    }

    // Add objective information
    transcriptionText += `Vitals are stable with blood pressure at ${actualBloodPressure} and heart rate at ${actualHeartRate} bpm. `;
    transcriptionText += `Oxygen saturation is ${actualOxygenSaturation}%. `;
    transcriptionText += `EECP session was well tolerated ${sideEffects.length > 0 ? "with " + sideEffects.join(" and ") : "with no complications"}. `;
    transcriptionText += `Patient completed full ${sessionDuration}-minute treatment. `;
    transcriptionText += `Cuff pressure was set to ${cuffPressure} mmHg. `;
    transcriptionText += `ECG showed normal sinus rhythm throughout. `;

    // Add plan
    if (session && session.sessionNumber < session.totalSessions - 5) {
        transcriptionText += `Plan is to continue with daily sessions as scheduled. `;
    } else {
        transcriptionText += `Plan is to complete the remaining ${session ? session.totalSessions - session.sessionNumber : 30} sessions and then reassess. `;
    }

    transcriptionText += `Recommended patient continue with prescribed medications and low-sodium diet.`;

    // Create structured SOAP note from the transcription
    const structuredNote = {
        subjective: `Patient reports ${symptomsList.length > 0 ? symptomsList.join(" and ") + ". " : ""}Chest pain has decreased from a ${painScaleStart} to a ${painScaleEnd} on the pain scale. ${Math.random() > 0.5 ? "No shortness of breath during normal activities." : "Mild shortness of breath only with significant exertion."}`,

        objective: `Vitals: BP ${actualBloodPressure}, HR ${actualHeartRate} bpm, O2 sat ${actualOxygenSaturation}%. EECP session was well tolerated ${sideEffects.length > 0 ? "with " + sideEffects.join(" and ") : "with no complications"}. Patient completed full ${sessionDuration}-minute treatment. Cuff pressure was set to ${cuffPressure} mmHg. ECG showed normal sinus rhythm throughout.`,

        assessment: `Patient showing ${session && session.sessionNumber > 10 ? "significant" : "gradual"} improvement in symptoms with EECP therapy. ${painScaleEnd < 3 ? "Excellent" : "Good"} response to treatment with reduced angina and ${symptomsList.length > 0 ? symptomsList.join(" and ") : "stable cardiovascular status"}.`,

        plan: `${session && session.sessionNumber < session.totalSessions - 5 ? "Continue with daily sessions as scheduled." : `Complete the remaining ${session ? session.totalSessions - session.sessionNumber : 30} sessions and then reassess.`} Recommended patient continue with prescribed medications and low-sodium diet. ${Math.random() > 0.7 ? "Consider follow-up echocardiogram after completion of therapy." : ""}`
    };

    // Calculate post-session vitals updates (preserving original logic)
    let vitalUpdates = {};

    // Calculate post-session heart rate if not already set
    if (!vitals.postHeartRate) {
        const postHeartRate = Math.floor(parseFloat(actualHeartRate) * (1 + (Math.random() * 0.1 - 0.05))); // +/- 5%
        vitalUpdates.postHeartRate = postHeartRate.toString();
    }

    // Calculate post-session blood pressure if not already set
    if (!vitals.postBloodPressure) {
        // Parse the BP like "120/80"
        const bpParts = actualBloodPressure.split('/');
        if (bpParts.length === 2) {
            const systolic = parseInt(bpParts[0], 10);
            const diastolic = parseInt(bpParts[1], 10);

            if (!isNaN(systolic) && !isNaN(diastolic)) {
                // Adjust slightly for post-session
                const postSystolic = Math.floor(systolic * (1 + (Math.random() * 0.1 - 0.05))); // +/- 5%
                const postDiastolic = Math.floor(diastolic * (1 + (Math.random() * 0.1 - 0.05))); // +/- 5%

                vitalUpdates.postBloodPressure = `${postSystolic}/${postDiastolic}`;
            }
        }
    }

    // Debug logging (preserving original behavior)
    console.log('Updated SOAP note:', transcriptionText);
    console.log('Updated structured note:', structuredNote);
    console.log('Calculated vital updates:', vitalUpdates);

    return {
        transcriptionText,
        structuredNote,
        vitalUpdates
    };
};