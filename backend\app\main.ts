import express from "express";
import cors from "cors";
import dotenv from "dotenv";
import patientRoutes from './patients/patientRoutes';

dotenv.config();
const app = express();
const port = process.env.PORT || 4000;

app.use(cors());
app.use(express.json());
app.use('/api/patients', patientRoutes);

app.get("/api/health", (_req, res) => {
  res.json({ status: "OK", time: new Date().toISOString() });
});

app.listen(port, () => {
  console.log(`🚀 Server listening at http://localhost:${port}`);
});