/**
 * @file SessionComplete.jsx
 * Component for displaying session completion summary.
 */

import { CheckCircle2 } from 'lucide-react';

/**
 * Renders the session completion view with summary and JSON output.
 *
 * @param {Object} props
 * @param {Object} props.sessionData - Completed session data
 * @param {Function} props.onStartNewSession - Callback to start a new session
 */
const SessionComplete = ({ sessionData, onStartNewSession }) => {
    return (
        <div className="max-w-2xl mx-auto">
            <div className="text-center mb-12">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <CheckCircle2 className="w-8 h-8 text-green-600" />
                </div>
                <h1 className="text-3xl font-light text-gray-900 mb-3">Session complete</h1>
                <p className="text-gray-500">All data has been recorded successfully</p>
            </div>

            <div className="bg-gray-50 rounded-2xl p-8 mb-8">
                <div className="grid grid-cols-2 gap-8 text-center">
                    <div>
                        <div className="text-2xl font-light text-gray-900">{sessionData.duration} min</div>
                        <div className="text-sm text-gray-500">Duration</div>
                    </div>
                    <div>
                        <div className="text-2xl font-light text-gray-900">#{sessionData.sessionNumber}</div>
                        <div className="text-sm text-gray-500">Session</div>
                    </div>
                </div>
                <div className="border-t border-gray-200 mt-6 pt-6 text-sm text-gray-600">
                    <div className="flex justify-between mb-2">
                        <span>Therapist</span>
                        <span>{sessionData.personnel.therapist}</span>
                    </div>
                    <div className="flex justify-between">
                        <span>Physician</span>
                        <span>{sessionData.personnel.physician}</span>
                    </div>
                </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 mb-8">
                <div className="flex items-center justify-between mb-4">
                    <h3 className="font-medium text-gray-900">JSON output</h3>
                    <button
                        onClick={() => navigator.clipboard.writeText(JSON.stringify(sessionData, null, 2))}
                        className="text-sm text-gray-500 hover:text-gray-700"
                    >
                        Copy
                    </button>
                </div>
                <pre className="text-xs text-gray-600 overflow-auto max-h-64 font-mono">
                    {JSON.stringify(sessionData, null, 2)}
                </pre>
            </div>

            <button
                onClick={onStartNewSession}
                className="w-full bg-black hover:bg-gray-800 text-white font-medium py-4 px-6 rounded-lg transition-colors"
            >
                Start new session
            </button>
        </div>
    );
};

export default SessionComplete;