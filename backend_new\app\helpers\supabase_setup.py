"""
Supabase Setup and Connection Module

This module provides Supabase client initialization, database connection,
and helper functions for interacting with Supabase services.
"""

import os
from typing import Optional, Dict, Any, List
from dotenv import load_dotenv
from supabase import create_client, Client
import asyncpg
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SupabaseConfig:
    """Configuration class for Supabase connection settings"""

    def __init__(self):
        self.url = os.getenv("SUPABASE_URL")
        self.key = os.getenv("SUPABASE_ANON_KEY")
        self.service_role_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        self.db_password = os.getenv("SUPABASE_DB_PASSWORD")

        # Validate required environment variables
        if not self.url:
            raise ValueError("SUPABASE_URL environment variable is required")
        if not self.key:
            raise ValueError("SUPABASE_ANON_KEY environment variable is required")

    @property
    def database_url(self) -> str:
        """Generate PostgreSQL connection URL for direct database access"""
        if not self.db_password:
            raise ValueError("SUPABASE_DB_PASSWORD is required for direct database access")

        # Extract project reference from Supabase URL
        # Format: https://your-project-ref.supabase.co
        project_ref = self.url.replace("https://", "").replace(".supabase.co", "")

        return f"postgresql://postgres:{self.db_password}@db.{project_ref}.supabase.co:5432/postgres"

class SupabaseClient:
    """Supabase client wrapper with connection management"""

    def __init__(self):
        self.config = SupabaseConfig()
        self._client: Optional[Client] = None
        self._db_pool: Optional[asyncpg.Pool] = None

    @property
    def client(self) -> Client:
        """Get or create Supabase client"""
        if self._client is None:
            self._client = create_client(self.config.url, self.config.key)
            logger.info("Supabase client initialized successfully")
        return self._client

    async def get_db_pool(self) -> asyncpg.Pool:
        """Get or create database connection pool for direct PostgreSQL access"""
        if self._db_pool is None:
            try:
                self._db_pool = await asyncpg.create_pool(
                    self.config.database_url,
                    min_size=1,
                    max_size=10,
                    command_timeout=60
                )
                logger.info("Database connection pool created successfully")
            except Exception as e:
                logger.error(f"Failed to create database pool: {e}")
                raise
        return self._db_pool

    async def close_db_pool(self):
        """Close database connection pool"""
        if self._db_pool:
            await self._db_pool.close()
            self._db_pool = None
            logger.info("Database connection pool closed")

    async def test_connection(self) -> Dict[str, Any]:
        """Test Supabase connection and return status"""
        try:
            # Test Supabase API connection
            self.client.table('_test_connection').select("*").limit(1).execute()
            api_status = "connected"
        except Exception as e:
            api_status = f"error: {str(e)}"

        try:
            # Test direct database connection
            pool = await self.get_db_pool()
            async with pool.acquire() as conn:
                result = await conn.fetchval("SELECT 1")
                db_status = "connected" if result == 1 else "error"
        except Exception as e:
            db_status = f"error: {str(e)}"

        return {
            "supabase_api": api_status,
            "database": db_status,
            "url": self.config.url,
            "project_ref": self.config.url.replace("https://", "").replace(".supabase.co", "")
        }

# Global Supabase client instance
supabase_client = SupabaseClient()

# Convenience functions for easy access
def get_supabase() -> Client:
    """Get Supabase client instance"""
    return supabase_client.client

async def get_db_pool() -> asyncpg.Pool:
    """Get database connection pool"""
    return await supabase_client.get_db_pool()

async def execute_query(query: str, *args) -> List[Dict[str, Any]]:
    """Execute a SQL query and return results"""
    pool = await get_db_pool()
    async with pool.acquire() as conn:
        rows = await conn.fetch(query, *args)
        return [dict(row) for row in rows]

async def execute_query_one(query: str, *args) -> Optional[Dict[str, Any]]:
    """Execute a SQL query and return single result"""
    pool = await get_db_pool()
    async with pool.acquire() as conn:
        row = await conn.fetchrow(query, *args)
        return dict(row) if row else None