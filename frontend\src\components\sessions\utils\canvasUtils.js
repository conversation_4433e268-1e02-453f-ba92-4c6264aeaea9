/**
 * Utility functions for canvas drawing operations in waveform displays.
 */

import {
    GRID_VERTICAL_DIVISIONS,
    GRID_HORIZONTAL_DIVISIONS,
    COLORS
} from '../constants/waveformConstants';

/**
 * Draws a grid pattern on the canvas for waveform reference.
 * Creates both vertical and horizontal guidelines with a baseline.
 *
 * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
 * @param {number} width - Canvas width in pixels
 * @param {number} height - Canvas height in pixels
 */
export const drawGrid = (ctx, width, height) => {
    ctx.save();

    ctx.beginPath();
    ctx.strokeStyle = COLORS.GRID;
    ctx.lineWidth = 1;

    // Draw vertical grid lines
    const verticalSpacing = width / GRID_VERTICAL_DIVISIONS;
    for (let x = 0; x < width; x += verticalSpacing) {
        ctx.moveTo(x, 0);
        ctx.lineTo(x, height);
    }

    // Draw horizontal grid lines
    const horizontalSpacing = height / GRID_HORIZONTAL_DIVISIONS;
    for (let y = 0; y < height; y += horizontalSpacing) {
        ctx.moveTo(0, y);
        ctx.lineTo(width, y);
    }

    ctx.stroke();

    // Draw the baseline (center horizontal line)
    ctx.beginPath();
    ctx.strokeStyle = COLORS.BASELINE;
    ctx.moveTo(0, height / 2);
    ctx.lineTo(width, height / 2);
    ctx.stroke();

    ctx.restore();
};

/**
 * Draws a waveform on the canvas with real-time scrolling effect.
 * Handles data scaling, offset positioning, and smooth line rendering.
 *
 * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
 * @param {HTMLCanvasElement} canvas - Canvas element for dimensions
 * @param {Array<number>} data - Waveform data points (-1 to 1)
 * @param {number} offset - Current scroll offset for animation
 * @param {string} color - Waveform line color
 */
export const drawWaveform = (ctx, canvas, data, offset, color) => {
    const width = canvas.width;
    const height = canvas.height;
    const midY = height / 2;
    const amplitude = height * 0.4; // Use 40% of canvas height for amplitude

    // Clear the canvas
    ctx.clearRect(0, 0, width, height);

    // Draw the grid first
    drawGrid(ctx, width, height);

    // Set up waveform drawing style
    ctx.beginPath();
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;

    // Draw the waveform line
    for (let x = 0; x < width; x++) {
        const dataIndex = (offset + x) % data.length;
        const y = midY - data[dataIndex] * amplitude;

        if (x === 0) {
            ctx.moveTo(x, y);
        } else {
            ctx.lineTo(x, y);
        }
    }

    ctx.stroke();
};

/**
 * Creates a composite image from multiple canvases for snapshot functionality.
 * Combines ECG and plethysmogram canvases into a single image.
 *
 * @param {HTMLCanvasElement} ecgCanvas - ECG waveform canvas
 * @param {HTMLCanvasElement} plethCanvas - Plethysmogram canvas
 * @param {number} spacing - Vertical spacing between canvases
 * @returns {string} Data URL of the composite image
 */
export const createCompositeSnapshot = (ecgCanvas, plethCanvas, spacing = 10) => {
    const snapshotCanvas = document.createElement('canvas');
    snapshotCanvas.width = ecgCanvas.width;
    snapshotCanvas.height = ecgCanvas.height + plethCanvas.height + spacing;

    const snapshotCtx = snapshotCanvas.getContext('2d');

    // Set white background
    snapshotCtx.fillStyle = 'white';
    snapshotCtx.fillRect(0, 0, snapshotCanvas.width, snapshotCanvas.height);

    // Draw labels
    snapshotCtx.fillStyle = 'black';
    snapshotCtx.font = '14px Arial';
    snapshotCtx.fillText('ECG', 10, 20);
    snapshotCtx.fillText('Plethysmogram', 10, ecgCanvas.height + spacing + 20);

    // Draw the ECG canvas
    snapshotCtx.drawImage(ecgCanvas, 0, 25);

    // Draw the plethysmogram canvas below it with spacing
    snapshotCtx.drawImage(plethCanvas, 0, ecgCanvas.height + spacing + 25);

    // Add timestamp
    const timestamp = new Date().toLocaleString();
    snapshotCtx.fillText(`Captured: ${timestamp}`, 10, snapshotCanvas.height - 10);

    return snapshotCanvas.toDataURL('image/png');
};

/**
 * Optimizes canvas for high DPI displays.
 * Adjusts canvas resolution for crisp rendering on retina displays.
 *
 * @param {HTMLCanvasElement} canvas - Canvas element to optimize
 * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
 * @param {number} width - Desired width in CSS pixels
 * @param {number} height - Desired height in CSS pixels
 */
export const optimizeCanvasForHiDPI = (canvas, ctx, width, height) => {
    const devicePixelRatio = window.devicePixelRatio || 1;

    // Set the actual canvas size in memory
    canvas.width = width * devicePixelRatio;
    canvas.height = height * devicePixelRatio;

    // Scale the canvas back down using CSS
    canvas.style.width = width + 'px';
    canvas.style.height = height + 'px';

    // Scale the drawing context so everything draws at the correct size
    ctx.scale(devicePixelRatio, devicePixelRatio);
};

/**
 * Calculates optimal canvas dimensions based on container size.
 * Maintains aspect ratio while fitting within container constraints.
 *
 * @param {HTMLElement} container - Container element
 * @param {number} aspectRatio - Desired width/height ratio
 * @returns {Object} Object with width and height properties
 */
export const calculateCanvasDimensions = (container, aspectRatio = 4) => {
    const containerWidth = container.clientWidth;
    const maxWidth = Math.min(containerWidth - 40, 800); // Leave 40px padding
    const height = maxWidth / aspectRatio;

    return {
        width: maxWidth,
        height: Math.max(height, 150) // Minimum height of 150px
    };
};