import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";

import { parseBloodPressure } from "../../sessions/utils/formatter";
import { localStorageService } from "../../../services/localStorageService";
import { calculateBmi } from "../utils/assessmentUtils";
import { useEffect, useState } from "react";
import {
  VITALS_BARCHART_OPTIONS,
  VITALS_CHART_PROPERTIES,
} from "../constants/commonStyles";
import {
  DEFAULT_PRE_SESSION_VITALS,
  BARCHART_DEFAULT_DATA,
  VITALS_CHART_LABELS,
} from "../constants/postTreatmentConstants";
import { useLocation } from "react-router-dom";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

/**
 * Prepares the data object for the Bar chart, using pre- and post-session vitals.
 * @param {Object} preSessionVitals - Vitals before the session
 * @param {Object} postSessionVitals - Vitals after the session
 * @returns {Object} Chart.js data object
 */
const parseDataForVitalsChart = (preSessionVitals, postSessionVitals) => {
  // Compose the data object for Chart.js
  return {
    labels: VITALS_CHART_LABELS,
    datasets: [
      {
        label: VITALS_CHART_PROPERTIES.before.label,
        data: [
          preSessionVitals.bmi,
          preSessionVitals.heartRate,
          preSessionVitals.systolic,
          preSessionVitals.diastolic,
          preSessionVitals.spo2,
        ],
        backgroundColor: VITALS_CHART_PROPERTIES.before.backgroundColor,
      },
      {
        label: VITALS_CHART_PROPERTIES.after.label,
        data: [
          postSessionVitals.bmi,
          postSessionVitals.heartRate,
          postSessionVitals.systolic,
          postSessionVitals.diastolic,
          postSessionVitals.spo2,
        ],
        backgroundColor: VITALS_CHART_PROPERTIES.after.backgroundColor,
      },
    ],
  };
};

/**
 * Normalizes a vitals object for use in the bar chart.
 * Ensures all required fields are present and parsed.
 * @param {Object} vitals - Raw vitals object
 * @returns {Object} Normalized vitals object
 */
const parseVitalsForBarChart = (vitals) => {
  const height = vitals.height;
  const weight = vitals.weight;
  // Parse blood pressure if available, otherwise use direct values
  const { systolic, diastolic } = vitals.bloodPressure
    ? parseBloodPressure(vitals.bloodPressure)
    : { systolic: vitals.systolic, diastolic: vitals.diastolic };
  return {
    bmi: calculateBmi(height, weight) ?? DEFAULT_PRE_SESSION_VITALS.bmi,
    heartRate: vitals.heartRate ?? DEFAULT_PRE_SESSION_VITALS.heartRate,
    systolic: systolic ?? DEFAULT_PRE_SESSION_VITALS.systolic,
    diastolic: diastolic ?? DEFAULT_PRE_SESSION_VITALS.diastolic,
    spo2: vitals.spo2 ?? DEFAULT_PRE_SESSION_VITALS.spo2,
  };
};

/**
 * Fetches and parses pre-session vitals for a given patient ID.
 * Returns default values if patient or vitals are not found.
 * @param {string} patientId - Patient identifier
 * @returns {Object} Normalized pre-session vitals
 */
const getPreSessionVitals = (patientId) => {
  // Fetch patient from local storage
  const patient = localStorageService.getPatientById(patientId);
  // If patient or vitals are missing, return defaults
  if (!patient || !patient.vitals) {
    return DEFAULT_PRE_SESSION_VITALS;
  }
  // Parse and normalize vitals
  const vitals = parseVitalsForBarChart(patient.vitals);
  return vitals ?? DEFAULT_PRE_SESSION_VITALS;
};

/**
 * Renders a bar chart comparing pre-session and post-session vitals.
 * - Fetches pre-session vitals based on patientId from the URL.
 * - Updates chart data when vitals or patientId change.
 *
 * @param {Object} props
 * @param {Object} props.vitals - Post-session vitals to compare
 * @param {Object} [props.options] - Chart.js options
 */
export default function VitalsBarChart({
  vitals,
  options = VITALS_BARCHART_OPTIONS,
}) {
  // Get patientId from the URL query string
  const location = useLocation();
  const patientId = new URLSearchParams(location.search).get("patientId");

  // State for pre-session vitals (fetched from storage)
  const [preSessionVitals, setPreSessionVitals] = useState(
    DEFAULT_PRE_SESSION_VITALS
  );
  // State for chart data
  const [chartData, setChartData] = useState(BARCHART_DEFAULT_DATA);

  // Fetch pre-session vitals when patientId changes
  useEffect(() => {
    if (patientId) {
      setPreSessionVitals(getPreSessionVitals(patientId));
    }
  }, [patientId]);

  // Update chart data when either vitals or preSessionVitals change
  useEffect(() => {
    const postSessionVitals = parseVitalsForBarChart(vitals);
    setChartData(parseDataForVitalsChart(preSessionVitals, postSessionVitals));
  }, [vitals, preSessionVitals]);

  // Render the bar chart
  return <Bar data={chartData} options={options} />;
}
