/**
 * PostTreatmentEvaluation.jsx
 * Module for managing post-treatment evaluation workflow.
 * Handles patient evaluation data entry, progress tracking, and PDF generation.
 */

import { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { usePDF } from 'react-to-pdf';
import {
  ArrowLeft,
  Calendar,
  Download,
  FileText,
  Printer,
  User,
  Clock,
  AlertCircle,
} from 'lucide-react';

// Component imports
import { TabNavigation, TabContent } from './evaluation-components/TabComponents';
import ProgressSidebar from './evaluation-components/ProgressSidebar';

// Context and service imports
import { useAuth } from '../../context/AuthContext';
import { localStorageService } from '../../services/localStorageService';

// Hooks
import { usePatientEvaluation } from './hooks/usePatientEvaluation';

// Utilities
import { getLatestSessionDate, isTreatmentComplete } from './utils/sessionUtils';
import {
  calculateActualProgress,
  updateEvaluationProgress,
  createOutcomeMeasuresFromEvaluation
} from './utils/evaluationUtils';

// Constants
import { EVALUATION_TABS } from './constants/postTreatmentConstants';

/**
 * Renders the main post-treatment evaluation interface.
 * Manages tabbed navigation for different evaluation sections and handles data persistence.
 * 
 * @returns {JSX.Element} The post-treatment evaluation component
 */
const PostTreatmentEvaluation = () => {
  // Router hooks
  const { search } = useLocation();
  const navigate = useNavigate();

  // Auth context
  const { currentUser } = useAuth();

  // URL parameters
  const patientId = new URLSearchParams(search).get('patientId');

  // PDF generation reference
  const { toPDF, targetRef } = usePDF({ filename: 'post-treatment-evaluation.pdf' });

  // Custom hook for patient data and evaluation state
  const {
    patient,
    isLoading,
    evaluation,
    setEvaluation,
    sessionCompletion
  } = usePatientEvaluation(patientId);

  // Component state
  const [activeTab, setActiveTab] = useState(EVALUATION_TABS.VITALS);

  /**
   * Handles input changes for evaluation form fields.
   * Updates the evaluation state with new field values and recalculates progress.
   * 
   * @param {string} section - The evaluation section (vitals, functionalTests, etc.)
   * @param {string} field - The specific field within the section
   * @param {string|number} value - The new value for the field
   */
  const handleInputChange = (section, field, value) => {
    setEvaluation(previousEvaluation => {
      // Update the field value
      const updatedEvaluation = {
        ...previousEvaluation,
        [section]: {
          ...previousEvaluation[section],
          [field]: value
        }
      };

      // Recalculate progress based on actual form completion
      return updateEvaluationProgress(updatedEvaluation);
    });
  };

  /**
   * Handles the print functionality for the evaluation report.
   * Triggers the browser's print dialog.
   */
  const handlePrint = () => {
    window.print();
  };

  /**
   * Handles PDF export functionality for the evaluation report.
   * Uses the usePDF hook to generate and download the PDF.
   */
  const handleExportPDF = () => {
    toPDF();
  };

  /**
   * Completes the evaluation process and saves all data.
   * Creates evaluation record, updates patient outcome measures, and navigates back.
   * Includes eligibility validation to ensure patient has completed sufficient sessions.
   */
  const handleCompleteEvaluation = async () => {
    try {
      // Validate eligibility before proceeding
      if (!sessionCompletion.isEligibleForEvaluation) {
        alert(
          `Patient must complete at least ${Math.ceil(sessionCompletion.total * 0.9)} sessions ` +
          `before post-treatment evaluation. Currently completed: ${sessionCompletion.completed}/${sessionCompletion.total}`
        );
        return;
      }

      // Check if evaluation is sufficiently complete
      const progressInfo = calculateActualProgress(evaluation);
      if (progressInfo.percentage < 70) {
        const confirmProceed = window.confirm(
          `Evaluation is only ${progressInfo.percentage}% complete. ` +
          `${progressInfo.completedSections}/${progressInfo.totalSections} sections are fully completed. ` +
          `Do you want to proceed anyway?`
        );
        if (!confirmProceed) {
          return;
        }
      }

      // Create comprehensive evaluation data object
      const evaluationData = {
        id: `eval_${Date.now()}`,
        date: new Date().toISOString(),
        author: currentUser?.name || 'System',
        summary: 'Post-treatment evaluation completed',
        patientId: patientId,
        sessionStats: sessionCompletion,
        progressInfo: progressInfo,
        data: evaluation
      };

      // Save evaluation using localStorage service
      localStorageService.addEvaluation(patientId, evaluationData);

      // Add evaluation completion note to patient record
      localStorageService.addNoteToPatient(patientId, {
        author: currentUser?.name || 'System',
        content: `Post-treatment evaluation completed (${progressInfo.percentage}% complete) after ${sessionCompletion.completed}/${sessionCompletion.total} sessions`
      });

      // Update patient's current outcome measures with latest evaluation data
      const updatedOutcomeMeasures = createOutcomeMeasuresFromEvaluation(
        evaluation,
        patient.outcomeMeasures
      );

      // Update patient record with new outcome measures
      localStorageService.updatePatient(patientId, {
        outcomeMeasures: updatedOutcomeMeasures
      });

      // Notify parent window if evaluation was opened in popup/modal
      if (window.opener && window.opener.handleNewEvaluation) {
        window.opener.handleNewEvaluation(evaluationData);
      }

      alert('Evaluation completed successfully!');
      navigate(`/patients/${patientId}`);
    } catch (error) {
      console.error('Error completing evaluation:', error);
      alert('Failed to complete evaluation. Please try again.');
    }
  };

  /**
   * Changes the active tab
   * @param {string} tabKey - The key of the tab to activate
   */
  const handleTabChange = (tabKey) => {
    setActiveTab(tabKey);
  };

  // Get real-time progress information
  const progressInfo = calculateActualProgress(evaluation);

  // Handle missing patient ID
  if (!patientId) {
    return (
      <div className="flex flex-col justify-center items-center h-screen">
        <div className="text-gray-600 text-xl mb-4">No Patient Selected</div>
        <div className="text-gray-500 mb-6">Please select a patient to start the evaluation</div>
        <button
          onClick={() => navigate('/patients')}
          className="bg-blue-600 text-white px-6 py-3 rounded hover:bg-blue-700"
        >
          Go to Patients List
        </button>
      </div>
    );
  }

  // Loading state render
  if (isLoading || !patient) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Main component render
  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 py-4 px-6 no-print">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <button
              onClick={() => navigate(-1)}
              className="mr-4 text-gray-500 hover:text-gray-700"
            >
              <ArrowLeft size={20} />
            </button>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">Post-Treatment Evaluation</h1>
              <div className="flex items-center mt-1">
                <div className="flex items-center text-sm text-gray-500 mr-4">
                  <User size={16} className="mr-1" />
                  <span>{patient.name}</span>
                </div>
                <div className="flex items-center text-sm text-gray-500 mr-4">
                  <Clock size={16} className="mr-1" />
                  <span>
                    {sessionCompletion.completed}/{sessionCompletion.total} Sessions Completed
                    {isTreatmentComplete(sessionCompletion) && (
                      <span className="ml-2 px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                        Treatment Complete
                      </span>
                    )}
                    {!sessionCompletion.isEligibleForEvaluation && (
                      <span className="ml-2 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                        {sessionCompletion.completionRate}% Complete
                      </span>
                    )}
                  </span>
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar size={16} className="mr-1" />
                  <span>
                    {getLatestSessionDate(patient) ?
                      `Last Session: ${getLatestSessionDate(patient)}` :
                      `Evaluation Date: ${new Date().toLocaleDateString()}`
                    }
                  </span>
                </div>
              </div>

              {/* Progress indicator in header */}
              <div className="mt-2 flex items-center text-sm">
                <div className="flex items-center">
                  <span className="text-gray-500 mr-2">Evaluation Progress:</span>
                  <div className="w-32 bg-gray-200 rounded-full h-2 mr-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${progressInfo.percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-blue-600 font-medium">{progressInfo.percentage}%</span>
                  <span className="text-gray-500 ml-2">
                    ({progressInfo.completedSections}/{progressInfo.totalSections} sections)
                  </span>
                </div>
              </div>

              {/* Eligibility Warning */}
              {!sessionCompletion.isEligibleForEvaluation && (
                <div className="mt-2 flex items-center text-sm text-amber-600 bg-amber-50 px-3 py-2 rounded-lg">
                  <AlertCircle size={16} className="mr-2 flex-shrink-0" />
                  <span>
                    Post-treatment evaluation is recommended after completing at least 90% of sessions.
                    Patient has completed {sessionCompletion.completionRate}% of treatment.
                  </span>
                </div>
              )}
            </div>
          </div>

          <div className="flex">
            <button
              onClick={handlePrint}
              className="flex items-center bg-white border border-gray-300 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 mr-3"
            >
              <Printer size={16} className="mr-1" />
              Print
            </button>
            <button
              onClick={handleExportPDF}
              className="flex items-center bg-white border border-gray-300 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 mr-3"
            >
              <Download size={16} className="mr-1" />
              Export PDF
            </button>
            <button
              onClick={handleCompleteEvaluation}
              disabled={!sessionCompletion.isEligibleForEvaluation}
              className={`flex items-center rounded-lg px-4 py-2 text-sm font-medium transition-colors ${sessionCompletion.isEligibleForEvaluation
                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              title={
                !sessionCompletion.isEligibleForEvaluation
                  ? `Complete at least ${Math.ceil(sessionCompletion.total * 0.9)} sessions before evaluation`
                  : 'Complete post-treatment evaluation'
              }
            >
              <FileText size={16} className="mr-1" />
              Complete Evaluation
            </button>
          </div>
        </div>
      </header>

      {/* Main content */}
      <div className="flex flex-1 overflow-hidden">
        <main className="flex-1 overflow-auto" ref={targetRef}>
          {/* Print Header */}
          <div className="hidden print-only bg-white p-6 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-2xl font-semibold text-gray-900">Post-Treatment Evaluation</h1>
                <div className="mt-2 text-sm text-gray-500">
                  <div>Patient: {patient.name}</div>
                  <div>
                    Sessions Completed: {sessionCompletion.completed}/{sessionCompletion.total}
                    {getLatestSessionDate(patient) && (
                      ` • Last Session: ${getLatestSessionDate(patient)}`
                    )}
                  </div>
                  <div>Evaluation Date: {new Date().toLocaleDateString()}</div>
                </div>
              </div>
              <div className="text-sm text-gray-500">
                <div>Generated on: {new Date().toLocaleString()}</div>
                {isTreatmentComplete(sessionCompletion) && (
                  <div className="mt-1 text-green-600 font-medium">Treatment Complete</div>
                )}
              </div>
            </div>
          </div>

          {/* Navigation Tabs */}
          <TabNavigation
            activeTab={activeTab}
            setActiveTab={handleTabChange}
          />

          {/* Tab content */}
          <div className="p-6">
            <TabContent
              activeTab={activeTab}
              evaluation={evaluation}
              handleInputChange={handleInputChange}
            />
          </div>
        </main>

        {/* Progress Sidebar */}
        <ProgressSidebar
          evaluation={evaluation}
          progressInfo={progressInfo}
        />
      </div>
    </div>
  );
};

export default PostTreatmentEvaluation;