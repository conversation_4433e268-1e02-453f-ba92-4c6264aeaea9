/**
 * Component for EECP parameter settings panel with organized controls.
 */

import ParameterControl from './ParameterControl';
import { PARAMETER_LIMITS } from '../constants/waveformConstants';

/**
 * Renders the EECP parameters settings panel with organized parameter controls.
 * Provides a clean interface for adjusting therapy parameters in real-time.
 *
 * @param {Object} props
 * @param {Object} props.parameters - Current parameter values
 * @param {Function} props.onParameterChange - Callback for parameter changes
 * @param {boolean} props.isVisible - Whether settings panel is visible
 */
const WaveformSettings = ({ parameters, onParameterChange, isVisible }) => {
    if (!isVisible) return null;

    /**
     * Parameter configuration with labels, tooltips, and formatting.
     */
    const parameterConfig = [
        {
            name: 'cuffPressure',
            label: 'Cuff Pressure',
            unit: 'mmHg',
            tooltip: 'Inflation pressure applied to the pneumatic cuffs',
            precision: 0,
            ...PARAMETER_LIMITS.cuffPressure
        },
        {
            name: 'inflationRatio',
            label: 'Inflation Ratio',
            unit: '',
            tooltip: 'Ratio of inflation to deflation time during cardiac cycle',
            precision: 1,
            ...PARAMETER_LIMITS.inflationRatio
        },
        {
            name: 'deflationTiming',
            label: 'Deflation Timing',
            unit: 'ms',
            tooltip: 'Timing offset for deflation relative to the R wave detection',
            precision: 0,
            ...PARAMETER_LIMITS.deflationTiming
        },
        {
            name: 'triggerDelay',
            label: 'Trigger Delay',
            unit: 'ms',
            tooltip: 'Delay between R wave detection and cuff inflation initiation',
            precision: 0,
            ...PARAMETER_LIMITS.triggerDelay
        }
    ];

    return (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200 
                    animate-fadeIn">
            <h4 className="text-sm font-medium text-gray-900 mb-3">
                EECP Parameters
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {parameterConfig.map((config) => (
                    <ParameterControl
                        key={config.name}
                        name={config.name}
                        label={config.label}
                        value={parameters[config.name]}
                        min={config.min}
                        max={config.max}
                        step={config.step}
                        unit={config.unit}
                        tooltip={config.tooltip}
                        precision={config.precision}
                        onChange={onParameterChange}
                    />
                ))}
            </div>

            {/* Parameter status indicators */}
            <div className="mt-4 pt-3 border-t border-gray-200">
                <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>Parameter status:</span>
                    <div className="flex items-center space-x-4">
                        <div className="flex items-center">
                            <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                            <span>Within limits</span>
                        </div>
                        <div className="flex items-center">
                            <div className="w-2 h-2 bg-blue-500 rounded-full mr-1"></div>
                            <span>Real-time sync</span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Animation for fade in */}
            <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(-10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .animate-fadeIn {
          animation: fadeIn 0.2s ease-out;
        }
      `}</style>
        </div>
    );
};

export default WaveformSettings;