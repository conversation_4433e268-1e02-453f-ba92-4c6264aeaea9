
/**
 * Service layer for session and patient data management.
 * Abstracts data source (mock vs real database) for easy switching.
 */

// Mock data (will be replaced with real API calls later)
const MOCK_PATIENTS = [
    {
        id: 'p1',
        name: '<PERSON>',
        age: 67,
        gender: 'Male',
        diagnosis: 'Coronary Artery Disease',
        lvef: 28,
        bnp: 450,
        status: 'Active',
        lastVisit: '2025-04-10',
        nextAppointment: '2025-04-16T10:00:00'
    },
    {
        id: 'p2',
        name: '<PERSON>',
        age: 72,
        gender: 'Female',
        diagnosis: 'Congestive Heart Failure',
        lvef: 32,
        bnp: 380,
        status: 'Active',
        lastVisit: '2025-04-12',
        nextAppointment: '2025-04-16T11:30:00'
    },
    {
        id: 'p3',
        name: '<PERSON>',
        age: 58,
        gender: 'Male',
        diagnosis: '<PERSON><PERSON>',
        lvef: 45,
        bnp: 120,
        status: 'Active',
        lastVisit: '2025-04-08',
        nextAppointment: '2025-04-16T14:00:00'
    }
];

const MOCK_SESSIONS = [
    {
        id: 'sess1',
        patientId: 'p1',
        sessionNumber: 3,
        totalSessions: 35,
        date: '2025-04-16T10:00:00',
        status: 'Scheduled',
        room: 'Room 101'
    },
    {
        id: 'sess2',
        patientId: 'p2',
        sessionNumber: 12,
        totalSessions: 35,
        date: '2025-04-16T11:30:00',
        status: 'Scheduled',
        room: 'Room 102'
    },
    {
        id: 'sess3',
        patientId: 'p3',
        sessionNumber: 7,
        totalSessions: 35,
        date: '2025-04-16T14:00:00',
        status: 'Scheduled',
        room: 'Room 101'
    }
];

/**
 * Fetches session data by ID.
 * 
 * @param {string} sessionId - Session identifier
 * @returns {Promise<Object|null>} Session data or null if not found
 */
export const fetchSessionById = async (sessionId) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));


    // const response = await fetch(`/api/sessions/${sessionId}`);
    // return response.json();

    return MOCK_SESSIONS.find(s => s.id === sessionId) || null;
};

/**
 * Fetches patient data by ID.
 * 
 * @param {string} patientId - Patient identifier
 * @returns {Promise<Object|null>} Patient data or null if not found
 */
export const fetchPatientById = async (patientId) => {

    // const response = await fetch(`/api/patients/${patientId}`);
    // return response.json();

    return MOCK_PATIENTS.find(p => p.id === patientId) || null;
};

/**
 * Saves session data to the database.
 * 
 * @param {Object} sessionData - Session data to save
 * @returns {Promise<Object>} Saved session data
 */
export const saveSessionData = async (sessionData) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // const response = await fetch(`/api/sessions/${sessionData.id}`, {
    //   method: 'PUT',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(sessionData)
    // });
    // return response.json();

    console.log('Session saved:', sessionData);
    return sessionData;
};