
/**
 * Modal component for AI-powered clinical note generation.
 * Handles audio recording, transcription display, and SOAP note visualization.
 */

import { FaMicrophone, FaMicrophoneSlash, FaSpinner } from 'react-icons/fa';
import { formatTime } from '../utils/formatter';

/**
 * Modal component for AI Scribe functionality.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.audioRecording - Audio recording hook object
 * @param {string} props.soapNote - Generated SOAP note text
 * @param {Object} props.structuredNote - Structured SOAP note object
 * @param {Function} props.onClose - Modal close handler
 * @param {Function} props.onAudioProcessing - Audio processing handler
 * @returns {JSX.Element} AI Scribe modal component
 */
const AIScribeModal = ({
    audioRecording,
    soapNote,
    structuredNote,
    onClose,
    onAudioProcessing
}) => {
    const {
        isRecording,
        recordingTime,
        isProcessing,
        startRecording,
        stopRecording
    } = audioRecording;

    /**
     * <PERSON><PERSON> start recording with audio processing callback.
     */
    const handleStartRecording = () => {
        startRecording(onAudioProcessing);
    };

    /**
     * Renders the recording state interface.
     */
    const renderRecordingState = () => (
        <div className="mt-4 flex flex-col items-center justify-center py-8">
            <div className="flex items-center mb-4">
                <div className="animate-pulse h-3 w-3 bg-red-600 rounded-full mr-2"></div>
                <p className="text-gray-700">Recording... {formatTime(recordingTime)}</p>
            </div>
            <button
                type="button"
                onClick={stopRecording}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
                <FaMicrophoneSlash className="mr-2" /> Stop Recording
            </button>
        </div>
    );

    /**
     * Renders the processing state interface.
     */
    const renderProcessingState = () => (
        <div className="mt-4 flex flex-col items-center justify-center py-8">
            <div className="flex items-center">
                <FaSpinner className="animate-spin h-5 w-5 text-primary mr-3" />
                <p className="text-gray-700">Processing audio...</p>
            </div>
        </div>
    );

    /**
     * Renders the initial state interface.
     */
    const renderInitialState = () => (
        <div className="mt-4 flex flex-col items-center justify-center py-8">
            <button
                type="button"
                onClick={handleStartRecording}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
                <FaMicrophone className="mr-2" /> Start Recording
            </button>
            <p className="mt-2 text-sm text-gray-500">
                Click to record your session notes
            </p>
        </div>
    );

    /**
     * Renders the SOAP note results interface.
     */
    const renderSOAPNoteResults = () => (
        <div className="mt-4">
            <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Transcription</h4>
                <p className="text-sm text-gray-600">{soapNote}</p>
            </div>

            <div className="mt-4 space-y-4">
                <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-1">Subjective</h4>
                    <div className="bg-blue-50 p-3 rounded">
                        <p className="text-sm text-gray-800">{structuredNote.subjective}</p>
                    </div>
                </div>

                <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-1">Objective</h4>
                    <div className="bg-green-50 p-3 rounded">
                        <p className="text-sm text-gray-800">{structuredNote.objective}</p>
                    </div>
                </div>

                <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-1">Assessment</h4>
                    <div className="bg-yellow-50 p-3 rounded">
                        <p className="text-sm text-gray-800">{structuredNote.assessment}</p>
                    </div>
                </div>

                <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-1">Plan</h4>
                    <div className="bg-purple-50 p-3 rounded">
                        <p className="text-sm text-gray-800">{structuredNote.plan}</p>
                    </div>
                </div>
            </div>
        </div>
    );

    /**
     * Determines which content to render based on current state.
     */
    const renderModalContent = () => {
        if (isRecording || isProcessing) {
            return isRecording ? renderRecordingState() : renderProcessingState();
        } else if (soapNote) {
            return renderSOAPNoteResults();
        } else {
            return renderInitialState();
        }
    };

    return (
        <div className="fixed z-10 inset-0 overflow-y-auto">
            <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div className="fixed inset-0 transition-opacity" aria-hidden="true">
                    <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
                </div>

                <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

                <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div className="sm:flex sm:items-start">
                            <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <h3 className="text-lg leading-6 font-medium text-gray-900 flex items-center">
                                    <FaMicrophone className="mr-2 text-primary" />
                                    AI-Scribe: Session Notes
                                </h3>

                                {renderModalContent()}
                            </div>
                        </div>
                    </div>
                    <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button
                            type="button"
                            onClick={onClose}
                            className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm"
                        >
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AIScribeModal;