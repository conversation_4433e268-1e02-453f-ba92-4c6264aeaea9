/**
 * @file SessionExecutionPanel.js
 * Component for managing EECP therapy session execution and monitoring.
 * Handles real-time session control, patient monitoring, vital signs recording, and AI-powered clinical note generation.
 */

import { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { useSessionData } from './hooks/useSessionData';
import { useAudioRecording } from './hooks/useAudioRecording';
import { saveSessionData } from '../../services/sessionService';
import { transcribeAudio, generateSOAPNote, generateSimulatedNote } from '../../services/AIService';
import { FaExclamationTriangle } from 'react-icons/fa';
import PatientInformationCard from './session-components/PatientInformationCard';
import PreSessionAssessment from './session-components/PreSessionAssessment';
import PostSessionAssessment from './session-components/PostSessionAssessment';
import SessionControls from './session-components/SessionControls';
import AIScribeModal from './session-components/AIScribeModal';
import EECPWaveformDisplay from './session-components/EECPWaveFormDisplay.jsx';


const SESSION_STATUS = {
  READY: 'ready',
  RUNNING: 'running',
  PAUSED: 'paused',
  COMPLETED: 'completed'
};

/**
 * Main component for EECP session execution and monitoring.
 * Provides comprehensive session management including patient monitoring,
 * real-time controls, vital signs recording, and AI-powered clinical documentation.
 * 
 * @returns {JSX.Element} Session execution panel component
 */
const SessionExecutionPanel = () => {
  // Router hooks
  const { sessionId } = useParams();
  const navigate = useNavigate();

  // Auth context
  const { medplum } = useAuth();

  // Custom hooks
  const {
    isLoading,
    session,
    patient,
    vitals,
    symptoms,
    updateVitals,
    updateSymptoms,
    setVitals
  } = useSessionData(sessionId);

  const audioRecording = useAudioRecording();

  // Component state
  const [sessionStatus, setSessionStatus] = useState(SESSION_STATUS.READY);
  const [showAIScribeModal, setShowAIScribeModal] = useState(false);
  const [soapNote, setSoapNote] = useState('');
  const [structuredNote, setStructuredNote] = useState({
    subjective: '',
    objective: '',
    assessment: '',
    plan: ''
  });

  /**
   * Handles audio processing for AI scribe.
   * @param {Blob} audioBlob - Audio data to process
   * @throws {Error} If processing fails
   * @throws {Error} If simulation fails
   * 
   */
  const handleAudioProcessing = async (audioBlob) => {
    audioRecording.setIsProcessing(true);

    try {
      await processWithOpenAI(audioBlob);
    } catch (apiError) {
      await processWithSimulation(apiError);
    } finally {
      audioRecording.setIsProcessing(false);
    }
  };

  /**
   * Processes audio using OpenAI APIs.
   * 
   * @param {Blob} audioBlob - Audio data to process
   * 
   */
  const processWithOpenAI = async (audioBlob) => {
    const transcriptionText = await transcribeAudio(audioBlob);
    const structuredSOAP = await generateSOAPNote(transcriptionText);

    setSoapNote(transcriptionText);
    setStructuredNote(structuredSOAP);

    console.log('Successfully processed with OpenAI API');
  };

  /**
   * Fallback processing using simulation.
   * 
   * @param {Error} originalError - The original API error
   */
  const processWithSimulation = async (originalError) => {
    try {
      console.error('OpenAI API failed:', originalError);
      alert(`Error processing audio with OpenAI API: ${originalError.message}. Falling back to simulation.`);

      const {
        transcriptionText,
        structuredNote: simNote,
        vitalUpdates
      } = await generateSimulatedNote(patient, session, vitals);

      setSoapNote(transcriptionText);
      setStructuredNote(simNote);

      if (vitalUpdates && Object.keys(vitalUpdates).length > 0) {
        setVitals(previousVitals => ({
          ...previousVitals,
          ...vitalUpdates
        }));
      }

      console.log('Successfully processed with simulation fallback');

    } catch (simulationError) {
      console.error('Simulation fallback also failed:', simulationError);
      alert('Error processing audio. Both API and simulation failed. Please try again.');

      // Re-throw to be handled by caller if needed
      throw new Error(`All processing methods failed. API: ${originalError.message}, Simulation: ${simulationError.message}`);
    }
  };

  /**
   * Saves session data and navigates back.
   */
  const handleSaveSession = async () => {
    try {
      const sessionData = {
        id: sessionId,
        vitals,
        symptoms,
        soapNote,
        structuredNote,
        status: sessionStatus,
        completedAt: new Date().toISOString()
      };

      await saveSessionData(sessionData);
      navigate('/nurse-dashboard');
    } catch (error) {
      console.error('Error saving session:', error);
      alert('Error saving session. Please try again.');
    }
  };

  // Loading state render
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Error state render when session or patient not found
  if (!session || !patient) {
    return (
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <FaExclamationTriangle className="h-5 w-5 text-yellow-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Session not found. Please select a valid session.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Main component render
  return (
    <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <header className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">EECP Session Execution</h1>
        <p className="mt-1 text-sm text-gray-500">
          Session #{session.sessionNumber} of {session.totalSessions} for {patient.name}
        </p>
      </header>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Patient Information */}
        <PatientInformationCard patient={patient} />

        {/* Pre-Session Vitals and Symptoms */}
        <PreSessionAssessment
          vitals={vitals}
          symptoms={symptoms}
          onVitalChange={updateVitals}
          onSymptomChange={updateSymptoms}
        />

        {/* Post-Session Vitals */}
        <PostSessionAssessment
          vitals={vitals}
          onVitalChange={updateVitals}
          onToggleAIScribe={() => setShowAIScribeModal(!showAIScribeModal)}
          onSaveSession={handleSaveSession}
        />
      </div>

      {/* Session Controls */}
      <SessionControls
        sessionStatus={sessionStatus}
        onStartSession={() => setSessionStatus(SESSION_STATUS.RUNNING)}
        onPauseSession={() => setSessionStatus(SESSION_STATUS.PAUSED)}
      />
      {/* Waveform Display */}
      <div className="mt-6">
        <EECPWaveformDisplay
          isRunning={sessionStatus === SESSION_STATUS.RUNNING}
          sessionData={{
            patientId: patient.id,
            patientName: patient.name,
            sessionNumber: session.sessionNumber,
            totalSessions: session.totalSessions,
            cuffPressure: 220
          }}
          onParameterChange={(params) => console.log('Parameter changed:', params)}
          onCaptureSnapshot={(dataUrl) => console.log('Snapshot captured')}
        />
      </div>

      {/* AI Scribe Modal */}
      {showAIScribeModal && (
        <AIScribeModal
          audioRecording={audioRecording}
          soapNote={soapNote}
          structuredNote={structuredNote}
          onClose={() => setShowAIScribeModal(false)}
          onAudioProcessing={handleAudioProcessing}
        />
      )}
    </div>
  );
};

export default SessionExecutionPanel;