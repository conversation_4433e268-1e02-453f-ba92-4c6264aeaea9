/**
 *@file VitalsTab.jsx
 * Demonstrates how the original component can be simplified using shared functions.
 */

import { useMemo, useEffect } from "react";
import { Activity, Heart, Droplets } from "lucide-react";

import { CARD_STYLES } from "../constants/commonStyles";
import {
  AssessmentInput,
  StatusIndicator,
  ClinicalNotes,
  SectionHeader,
} from "../evaluation-components/commonComponents";
import {
  calculateBmi,
  getBmiStatus,
  getBloodPressureStatus,
} from "../utils/assessmentUtils";
import VitalsBarChart from "../evaluation-components/BarChart";

/**
 * BMI calculator component using shared utilities.
 *
 * @param {Object} props
 * @param {string} props.height - Height value
 * @param {string} props.weight - Weight value
 * @param {string} props.bmi - BMI value
 * @param {Function} props.onChange - Change handler function
 */
const BmiCalculator = ({ height, weight, bmi, onChange }) => {
  // Calculate BMI using shared utility
  const calculatedBmi = useMemo(() => {
    return calculateBmi(height, weight);
  }, [height, weight]);

  // Update BMI field when calculated value changes
  useEffect(() => {
    if (calculatedBmi && calculatedBmi !== bmi) {
      onChange("bmi", calculatedBmi);
    }
  }, [calculatedBmi, bmi, onChange]);

  // Get BMI status using shared utility
  const bmiStatus = getBmiStatus(bmi);

  return (
    <div className="space-y-2">
      <AssessmentInput
        label="BMI (Calculated)"
        value={bmi}
        onChange={onChange}
        fieldName="bmi"
        placeholder="Auto-calculated"
        unit="kg/m²"
        type="number"
      />

      {bmiStatus && (
        <div className={`p-2 rounded border ${bmiStatus.color}`}>
          <div className="flex items-center">
            <Activity size={14} className="mr-2" />
            <span className="text-sm font-medium">{bmiStatus.message}</span>
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Blood pressure status component using shared utilities.
 *
 * @param {Object} props
 * @param {string} props.systolic - Systolic blood pressure
 * @param {string} props.diastolic - Diastolic blood pressure
 */
const BloodPressureStatus = ({ systolic, diastolic }) => {
  const bpStatus = getBloodPressureStatus(systolic, diastolic);

  if (!bpStatus) return null;

  return (
    <div className={`p-3 rounded-lg border ${bpStatus.colorClass} mt-2`}>
      <div className="flex items-center">
        <Heart size={16} className="mr-2" />
        <div>
          <span className="text-sm font-medium">
            {systolic}/{diastolic} mmHg - {bpStatus.category}
          </span>
          <div className="text-xs opacity-75">{bpStatus.message}</div>
        </div>
      </div>
    </div>
  );
};

/**
 * Vitals tab component for post-treatment evaluation.
 *
 * @param {Object} props
 * @param {Object} props.vitals - Vitals data object
 * @param {Function} props.onChange - Change handler function
 */
const VitalsTab = ({ vitals, onChange }) => {
  return (
    <div className="space-y-6">
      {/* Header using common component */}
      <SectionHeader
        title="Vital Signs Assessment"
        icon={Activity}
        iconColor="text-blue-600"
      />

      {/* Basic Measurements */}
      <div className={CARD_STYLES.SECTION}>
        <h4 className="text-lg font-medium text-gray-800 mb-4">
          Basic Measurements
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <AssessmentInput
            label="Height"
            value={vitals.height}
            onChange={onChange}
            fieldName="height"
            placeholder="175"
            unit="cm"
            min="50"
            max="250"
            description="Height in centimeters"
          />
          <AssessmentInput
            label="Weight"
            value={vitals.weight}
            onChange={onChange}
            fieldName="weight"
            placeholder="70"
            unit="kg"
            min="20"
            max="300"
            description="Weight in kilograms"
          />
          <BmiCalculator
            height={vitals.height}
            weight={vitals.weight}
            bmi={vitals.bmi}
            onChange={onChange}
          />
        </div>
      </div>

      {/* Cardiovascular Measurements */}
      <div className={CARD_STYLES.SECTION}>
        <h4 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
          <Heart className="mr-2 text-red-500" size={20} />
          Cardiovascular Measurements
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <AssessmentInput
            label="Systolic Blood Pressure"
            value={vitals.systolic}
            onChange={onChange}
            fieldName="systolic"
            placeholder="120"
            unit="mmHg"
            min="60"
            max="250"
            description="Upper blood pressure reading"
          />
          <AssessmentInput
            label="Diastolic Blood Pressure"
            value={vitals.diastolic}
            onChange={onChange}
            fieldName="diastolic"
            placeholder="80"
            unit="mmHg"
            min="40"
            max="150"
            description="Lower blood pressure reading"
          />
          <AssessmentInput
            label="Heart Rate"
            value={vitals.heartRate}
            onChange={onChange}
            fieldName="heartRate"
            placeholder="72"
            unit="bpm"
            min="40"
            max="200"
            description="Resting heart rate"
          />
        </div>

        {/* Status indicators using common components */}
        <BloodPressureStatus
          systolic={vitals.systolic}
          diastolic={vitals.diastolic}
        />

        <StatusIndicator
          value={vitals.heartRate}
          range={{
            low: 60,
            high: 100,
            lowMessage: "Bradycardia - slow heart rate",
            normalMessage: "Normal resting heart rate",
            highMessage: "Tachycardia - elevated heart rate",
          }}
          label="Heart Rate"
          unit="bpm"
        />
      </div>

      {/* Respiratory Measurements */}
      <div className={CARD_STYLES.SECTION}>
        <h4 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
          <Droplets className="mr-2 text-blue-500" size={20} />
          Respiratory Measurements
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <AssessmentInput
            label="Oxygen Saturation (SpO2)"
            value={vitals.spo2}
            onChange={onChange}
            fieldName="spo2"
            placeholder="98"
            unit="%"
            min="70"
            max="100"
            description="Blood oxygen saturation percentage"
          />
        </div>

        <StatusIndicator
          value={vitals.spo2}
          range={{
            low: 95,
            high: 100,
            lowMessage: "Low oxygen saturation - may require attention",
            normalMessage: "Normal oxygen saturation",
            highMessage: "Normal oxygen saturation",
          }}
          label="SpO2"
          unit="%"
        />
      </div>
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-700 mb-2">
          Vitals Comparison Chart
        </h4>
        <div className="aspect-[21/9] bg-white rounded-lg p-4 border border-gray-200">
          <div className="w-full h-full flex items-center justify-center text-gray-400">
            <VitalsBarChart vitals={vitals} />
          </div>
        </div>
      </div>

      {/* Clinical Notes using common component */}
      <ClinicalNotes
        value={vitals.notes}
        onChange={onChange}
        placeholder="Enter clinical observations, trends, patient symptoms, or any relevant vital signs information..."
      />
    </div>
  );
};

export default VitalsTab;
