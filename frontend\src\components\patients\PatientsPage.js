import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { PATIENT_STATUS, getStatusConfig, getPatientStatusSummary } from '../../data/mockData';
import PatientNotes from './PatientNotes';

/**
 * PatientsPage - Displays a list of patients fetched from the backend (Medplum).
 * Handles search, status filtering, and navigation to patient details.
 */
export default function PatientsPage() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [expandedPatient, setExpandedPatient] = useState(null);
  const [patients, setPatients] = useState([]);

  // Fetch patients from backend API (Medplum)
  useEffect(() => {
    const doctorId = localStorage.getItem('syncore_doctorId');
    fetch(`http://localhost:4000/api/patients${doctorId ? `?doctorId=${doctorId}` : ''}`)
      .then(res => res.json())
      .then(data => {
        console.log('Fetched patients:', data);
        setPatients(data);
      })
      .catch(err => {
        console.error('Failed to fetch patients from backend:', err);
        setPatients([]);
      });
  }, []);

  // Filter patients based on search term and status
  const filteredPatients = patients.filter(patient => {
    const matchesSearch = patient.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || patient.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  // Patient Card Component
  const PatientCard = ({ patient }) => {
    const statusSummary = getPatientStatusSummary(patient);
    const statusConfig = getStatusConfig(patient.status);

    // Only keep Book Appointment and View Details actions
    const handleBookAppointment = (e, patientId) => {
      e.stopPropagation();
      navigate(`/appointments/new?patientId=${patientId}`);
    };

    return (
      <div
        className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 mb-4 cursor-pointer hover:shadow-lg transition-shadow"
        onClick={() => setExpandedPatient(expandedPatient === patient.id ? null : patient.id)}
      >
        <div className={`bg-${statusConfig.color}-600 px-4 py-2 text-white flex justify-between items-center`}>
          <div className="flex items-center">
            <span className={`bg-${statusConfig.color}-400 h-2.5 w-2.5 rounded-full mr-2`}></span>
            <h3 className="font-medium text-sm">{patient.name} • {statusSummary.label}</h3>
          </div>
          <div className="text-xs font-medium bg-${statusConfig.color}-500 px-2 py-0.5 rounded-full">
            {patient.id}
          </div>
        </div>

        <div className="p-4">
          <div className="flex">
            {/* Left column: Patient identity + summary */}
            <div className="w-1/2 pr-3 flex flex-col justify-between">
              <div>
                <div className="flex items-center mb-2">
                  <div className={`h-12 w-12 rounded-full bg-${statusConfig.color}-100 text-${statusConfig.color}-800 flex items-center justify-center font-bold text-lg mr-3 shadow-sm`}>
                    {patient.name.split(' ').map(n => n[0]).join('')}
                  </div>
                  <div>
                    <div className="font-bold text-gray-900">{patient.name}</div>
                    <div className="text-gray-500 text-sm">{patient.age} yrs • {patient.gender}</div>
                  </div>
                </div>
                {/* AI Summary */}
                <div className={`flex flex-1 items-stretch`}>
                  <div className={`bg-${statusConfig.color}-50 border-l-3 border-${statusConfig.color}-500 pl-2 py-1 pr-1 rounded-r w-full flex items-center`}>
                    <svg className="h-3 w-3 mr-1 text-${statusConfig.color}-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    <p className="line-clamp-2 text-base font-medium w-full" style={{ minHeight: '100px', display: 'flex', alignItems: 'center' }}>{patient.summary}</p>
                  </div>
                </div>
              </div>
            </div>
            {/* Right column: Key metrics + action buttons */}
            <div className="w-1/2 pl-3 border-l border-gray-100">
              <div className="text-base font-bold text-gray-800 mb-2 tracking-wide">KEY METRICS</div>
              <div className="grid grid-cols-2 gap-4 mb-2">
                {/* Left metrics */}
                <div>
                  <div className="mb-4">
                    <div className="flex justify-between items-end mb-1">
                      <span className="text-xs text-gray-600">CCS Angina Class</span>
                    </div>
                    <div className="flex items-end space-x-2">
                      <span className="text-2xl font-extrabold text-blue-700 drop-shadow-sm">
                        {patient.metrics?.ccsClass?.current || 'N/A'}
                      </span>
                      <span className="text-xs text-gray-400">Class</span>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between items-end mb-1">
                      <span className="text-xs text-gray-600">LVEF</span>
                    </div>
                    <div className="flex items-end space-x-2">
                      <span className="text-2xl font-extrabold text-blue-700 drop-shadow-sm">
                        {patient.metrics?.lvef?.current || 'N/A'}
                      </span>
                      <span className="text-xs text-gray-400">%</span>
                    </div>
                  </div>
                </div>
                {/* Right metrics */}
                <div>
                  <div className="mb-4">
                    <div className="flex justify-between items-end mb-1">
                      <span className="text-xs text-gray-600">Angina Episodes</span>
                    </div>
                    <div className="flex items-end space-x-2">
                      <span className="text-2xl font-extrabold text-blue-700 drop-shadow-sm">
                        {patient.metrics?.anginaEpisodes?.current || 'N/A'}
                      </span>
                      <span className="text-xs text-gray-400">/week</span>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between items-end mb-1">
                      <span className="text-xs text-gray-600">Blood Pressure</span>
                    </div>
                    <div className="flex items-end space-x-2">
                      <span className="text-2xl font-extrabold text-blue-700 drop-shadow-sm">
                        {patient.metrics?.bp?.current || 'N/A'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              {/* Only Book Appointment and View Details */}
              <div className="flex justify-end space-x-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(`/patients/${patient.id}`);
                  }}
                  className="px-3 py-1 bg-blue-600 text-white font-medium rounded hover:bg-blue-700 transition-colors text-xs"
                >
                  View Details
                </button>
                <button
                  onClick={(e) => handleBookAppointment(e, patient.id)}
                  className="px-3 py-1 bg-purple-600 text-white font-medium rounded hover:bg-purple-700 transition-colors text-xs"
                >
                  Book Appointment
                </button>
              </div>
            </div>
          </div>
          {/* Notes Section - Only shown when expanded */}
          {expandedPatient === patient.id && (
            <PatientNotes
              patient={patient}
              onNotesUpdate={() => {}}
            />
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6 flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Patients</h1>
            <p className="mt-1 text-sm text-gray-500">
              Manage and monitor all patients in the EECP program
            </p>
          </div>
          <button
            onClick={() => navigate('/patients/new')}
            className="px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors flex items-center"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
            </svg>
            Add Patient
          </button>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search patients..."
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="w-full sm:w-48">
              <select
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <option value="all">All Status</option>
                {Object.values(PATIENT_STATUS).map(status => (
                  <option key={status} value={status}>
                    {getStatusConfig(status).label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Patient Cards */}
        <div className="space-y-4">
          {filteredPatients.map(patient => (
            <PatientCard key={patient.id} patient={patient} />
          ))}
        </div>
      </div>
    </div>
  );
}
