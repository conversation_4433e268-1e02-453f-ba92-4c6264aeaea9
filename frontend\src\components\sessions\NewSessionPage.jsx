/**
 * @file NewSessionPage.jsx
 * Main component for EECP session workflow management.
 */

import { useState, useEffect, useRef } from 'react';
import { Clock } from 'lucide-react';
import {
    DEFAULT_SESSION_DATA,
    API_BASE_URL,
    SESSION_STAGES,
    TIMER_INTERVALS
} from './constants/sessionConstants';
// Component imports
import PreSessionForm from './session-components/PreSessionForm';
import ActiveSession from './session-components/ActiveSession';
import DuringForm from './session-components/DuringForm';
import PostSessionForm from './session-components/PostSessionForm';
import SessionComplete from './session-components/SessionComplete';

/**
 * Main EECP session page component.
 * Manages the complete session workflow from patient validation to completion.
 *
 * @returns {JSX.Element} New session page component
 */
const NewSessionPage = () => {
    // State from original component
    const [currentStage, setCurrentStage] = useState(SESSION_STAGES.PRE_SESSION);
    const [sessionStartTime, setSessionStartTime] = useState(null);
    const [elapsedTime, setElapsedTime] = useState(0);
    const [patientId, setPatientId] = useState('');
    const [patientInfo, setPatientInfo] = useState(null);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(null);
    const [sessionData, setSessionData] = useState(DEFAULT_SESSION_DATA);

    // Timer refs from original
    const timerRef = useRef(null);
    const [showPrompt, setShowPrompt] = useState(false);
    const [promptStage, setPromptStage] = useState('');

    // Timer effect 
    useEffect(() => {
        if (currentStage === SESSION_STAGES.ACTIVE && sessionStartTime) {
            timerRef.current = setInterval(() => {
                const now = new Date();
                const elapsed = Math.floor((now - sessionStartTime) / 1000);
                setElapsedTime(elapsed);

                if (elapsed === TIMER_INTERVALS.CHECKPOINT_15_MIN && promptStage !== '15min') {
                    setPromptStage('15min');
                    setShowPrompt(true);
                } else if (elapsed === TIMER_INTERVALS.CHECKPOINT_45_MIN && promptStage !== '45min') {
                    setPromptStage('45min');
                    setShowPrompt(true);
                }
            }, TIMER_INTERVALS.UPDATE_INTERVAL);
        }

        return () => {
            if (timerRef.current) {
                clearInterval(timerRef.current);
            }
        };
    }, [currentStage, sessionStartTime, promptStage]);

    /**
     * Formats elapsed time in seconds to HH:MM:SS format.
     *
     * @param {number} seconds - Total elapsed seconds
     * @returns {string} Formatted time string
     */
    const formatTime = (seconds) => {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    /**
     * Handles input changes for session measurements.
     *
     * @param {string} section - Measurement section
     * @param {string} field - Field name
     * @param {any} value - New value
     */
    const handleInputChange = (section, field, value) => {
        setSessionData(prev => ({
            ...prev,
            measurements: {
                ...prev.measurements,
                [section]: {
                    ...prev.measurements[section],
                    [field]: value
                }
            }
        }));
    };

    /**
     * Handles nested input changes (e.g., blood pressure).
     *
     * @param {string} section - Measurement section
     * @param {string} parentField - Parent field name
     * @param {string} childField - Child field name
     * @param {any} value - New value
     */
    const handleNestedInputChange = (section, parentField, childField, value) => {
        setSessionData(prev => ({
            ...prev,
            measurements: {
                ...prev.measurements,
                [section]: {
                    ...prev.measurements[section],
                    [parentField]: {
                        ...prev.measurements[section][parentField],
                        [childField]: value
                    }
                }
            }
        }));
    };

    /**
     * Handles personnel changes.
     *
     * @param {string} field - Personnel field
     * @param {string} value - New value
     */
    const handlePersonnelChange = (field, value) => {
        setSessionData(prev => ({
            ...prev,
            personnel: {
                ...prev.personnel,
                [field]: value
            }
        }));
    };

    /**
     * Handles notes changes.
     *
     * @param {string} notes - New notes value
     */
    const handleNotesChange = (notes) => {
        setSessionData(prev => ({ ...prev, notes }));
    };

    /**
     * Starts the session .
     */
    const startSession = () => {
        // Validate that patient info exists before starting session
        if (!patientInfo || !success) {
            setError('Cannot start session: Please validate patient first.');
            return;
        }

        const now = new Date();
        setSessionStartTime(now);
        setSessionData(prev => ({
            ...prev,
            startTime: now.toTimeString().split(' ')[0],
            measurements: {
                ...prev.measurements,
                preEECP: {
                    ...prev.measurements.preEECP,
                    timestamp: now.toISOString()
                }
            }
        }));

        // Clear any previous errors
        setError(null);
        setCurrentStage(SESSION_STAGES.ACTIVE);
    };

    /**
     * Stops the session .
     */
    const stopSession = () => {
        const now = new Date();
        const duration = Math.floor((now - sessionStartTime) / 60000);

        setSessionData(prev => ({
            ...prev,
            endTime: now.toTimeString().split(' ')[0],
            duration: duration,
            status: 'completed'
        }));
        setCurrentStage(SESSION_STAGES.POST_SESSION);

        if (timerRef.current) {
            clearInterval(timerRef.current);
        }
    };

    /**
     * Handles checkpoint data prompt .
     */
    const handlePromptData = () => {
        const now = new Date();
        const section = promptStage === '15min' ? 'during15Min' : 'during45Min';

        setSessionData(prev => ({
            ...prev,
            measurements: {
                ...prev.measurements,
                [section]: {
                    ...prev.measurements[section],
                    timestamp: now.toISOString()
                }
            }
        }));

        setShowPrompt(false);
        setCurrentStage(promptStage === '15min' ? SESSION_STAGES.DURING_15 : SESSION_STAGES.DURING_45);
    };

    /**
     * Completes the session and saves data.
     */
    const completeSession = async () => {
        // Validate that patient info exists
        if (!patientInfo) {
            setError('Cannot complete session: No patient information found. Please validate patient first.');
            return;
        }

        if (!patientId) {
            setError('Cannot complete session: No patient ID found. Please validate patient first.');
            return;
        }

        const now = new Date();
        setSessionData(prev => ({
            ...prev,
            measurements: {
                ...prev.measurements,
                postEECP: {
                    ...prev.measurements.postEECP,
                    timestamp: now.toISOString()
                }
            }
        }));

        console.log(sessionData);

        try {
            // Create a copy of patientInfo to avoid mutating original
            const updatedPatientInfo = { ...patientInfo };

            // Ensure sessions array exists
            if (!Array.isArray(updatedPatientInfo.sessions)) {
                updatedPatientInfo.sessions = [];
            }

            // Add new session to the copy
            updatedPatientInfo.sessions.push(sessionData);

            const response = await fetch(`${API_BASE_URL}/patients/${patientId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(updatedPatientInfo)
            });

            if (!response.ok) {
                throw new Error(`Failed to save session: ${response.status} ${response.statusText}`);
            }

            // Clear any previous errors and set success
            setError(null);
            setCurrentStage(SESSION_STAGES.COMPLETE);
        } catch (error) {
            setError(`Failed to save session data: ${error.message}`);
            setSuccess(null);
            console.error('Error saving session:', error);
        }
    };

    /**
     * Retrieves patient from database.
     *
     * @param {string} patientId - Patient ID to retrieve
     */
    const retrievePatient = async (patientId) => {
        // Validate patient ID
        if (!patientId) {
            setError('Please enter a valid Patient ID');
            setSuccess(null);
            return;
        }

        // Clear previous messages
        setError(null);
        setSuccess(null);

        try {
            const response = await fetch(`${API_BASE_URL}/patients/${patientId.trim()}`);

            if (!response.ok) {
                if (response.status === 404) {
                    throw new Error(`Patient with ID "${patientId}" not found`);
                }
                throw new Error(`Failed to fetch patient: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            console.log(data);
            setPatientInfo(data);
            setSuccess('Patient retrieved successfully');
        } catch (error) {
            setError(error.message);
            setPatientInfo(null);
            console.error('Error retrieving patient:', error);
        }
    };

    /**
     * Handles patient ID form submission.
     *
     * @param {Event} e - Form submit event
     */
    const handleIdSubmit = (e) => {
        e.preventDefault();

        const trimmedId = patientId.trim();
        if (!trimmedId) {
            setError('Please enter a Patient ID');
            return;
        }

        retrievePatient(trimmedId);
    };

    /**
     * Handles starting a new session.
     */
    const handleStartNewSession = () => {
        setCurrentStage(SESSION_STAGES.PRE_SESSION);
        setElapsedTime(0);
        setSessionStartTime(null);
        setShowPrompt(false);
        setPromptStage('');

        // Reset patient data for new session
        setPatientId('');
        setPatientInfo(null);
        setError(null);
        setSuccess(null);

        setSessionData({
            ...sessionData,
            sessionNumber: sessionData.sessionNumber + 1,
            notes: '',
            measurements: {
                preEECP: {
                    timestamp: '', o2Liters: '', weight: '', weightUnit: 'lbs',
                    bloodPressure: { systolic: '', diastolic: '' }, pulseRate: '', spo2: ''
                },
                during15Min: { timestamp: '', dsPeak: '', dsArea: '', pulseRate: '', appliedPressure: '' },
                during45Min: { timestamp: '', dsPeak: '', dsArea: '', pulseRate: '', appliedPressure: '' },
                postEECP: {
                    timestamp: '', bloodPressure: { systolic: '', diastolic: '' },
                    pulseRate: '', spo2: '', skinCondition: 'Normal - No irritation', electrodeInducedArtefact: false
                }
            }
        });
    };

    return (
        <div className="min-h-screen bg-white">
            <div className="px-6 py-12">
                {/* Time Prompt Modal */}
                {showPrompt && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white rounded-2xl p-8 max-w-sm w-full mx-4 text-center">
                            <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <Clock className="w-6 h-6 text-orange-600" />
                            </div>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">Time checkpoint</h3>
                            <p className="text-gray-500 mb-6">
                                Collect {promptStage === '15min' ? '15-minute' : '45-minute'} measurements now
                            </p>
                            <button
                                onClick={handlePromptData}
                                className="w-full bg-black hover:bg-gray-800 text-white font-medium py-3 px-6 rounded-lg transition-colors"
                            >
                                Continue
                            </button>
                        </div>
                    </div>
                )}

                {/* Stage Content */}
                {currentStage === SESSION_STAGES.PRE_SESSION && (
                    <PreSessionForm
                        sessionData={sessionData}
                        patientId={patientId}
                        error={error}
                        success={success}
                        onPatientIdChange={setPatientId}
                        onIdSubmit={handleIdSubmit}
                        onInputChange={handleInputChange}
                        onNestedInputChange={handleNestedInputChange}
                        onPersonnelChange={handlePersonnelChange}
                        onStartSession={startSession}
                    />
                )}

                {currentStage === SESSION_STAGES.ACTIVE && (
                    <ActiveSession
                        elapsedTime={elapsedTime}
                        formatTime={formatTime}
                        onStopSession={stopSession}
                    />
                )}

                {currentStage === SESSION_STAGES.DURING_15 && (
                    <DuringForm
                        stage="15"
                        sessionData={sessionData}
                        onInputChange={handleInputChange}
                        onContinue={() => setCurrentStage(SESSION_STAGES.ACTIVE)}
                    />
                )}

                {currentStage === SESSION_STAGES.DURING_45 && (
                    <DuringForm
                        stage="45"
                        sessionData={sessionData}
                        onInputChange={handleInputChange}
                        onContinue={() => setCurrentStage(SESSION_STAGES.ACTIVE)}
                    />
                )}

                {currentStage === SESSION_STAGES.POST_SESSION && (
                    <PostSessionForm
                        sessionData={sessionData}
                        error={error}
                        onInputChange={handleInputChange}
                        onNestedInputChange={handleNestedInputChange}
                        onNotesChange={handleNotesChange}
                        onCompleteSession={completeSession}
                    />
                )}

                {currentStage === SESSION_STAGES.COMPLETE && (
                    <SessionComplete
                        sessionData={sessionData}
                        onStartNewSession={handleStartNewSession}
                    />
                )}
            </div>
        </div>
    );
};

export default NewSessionPage;