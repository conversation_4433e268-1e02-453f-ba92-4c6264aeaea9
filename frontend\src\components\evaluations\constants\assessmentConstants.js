/**
 *@file assessmentConstants.js
 * Constants and reference data for medical assessments.
 */

// Symptom assessment scales used across symptom evaluations
export const SYMPTOM_SCALES = {
    FREQUENCY: [
        { value: '', label: 'Select Frequency', score: 0 },
        { value: '0', label: 'Never', score: 0 },
        { value: '1', label: 'Rarely (1-2/week)', score: 1 },
        { value: '2', label: 'Sometimes (3-4/week)', score: 2 },
        { value: '3', label: 'Often (5-6/week)', score: 3 },
        { value: '4', label: 'Daily', score: 4 },
        { value: '5', label: 'Multiple times daily', score: 5 }
    ],
    SEVERITY: [
        { value: '', label: 'Select Severity', score: 0 },
        { value: '0', label: 'None', score: 0 },
        { value: '1', label: 'Mild', score: 1 },
        { value: '2', label: 'Moderate', score: 2 },
        { value: '3', label: 'Severe', score: 3 },
        { value: '4', label: 'Very Severe', score: 4 }
    ]
};

// Quality of life assessment scales
export const QUALITY_SCALES = {
    ENERGY_LEVEL: [
        { value: '', label: 'Select Energy Level', score: 0 },
        { value: '1', label: 'Very Low', score: 1 },
        { value: '2', label: 'Low', score: 2 },
        { value: '3', label: 'Moderate', score: 3 },
        { value: '4', label: 'High', score: 4 },
        { value: '5', label: 'Very High', score: 5 }
    ],
    SLEEP_QUALITY: [
        { value: '', label: 'Select Sleep Quality', score: 0 },
        { value: '1', label: 'Very Poor', score: 1 },
        { value: '2', label: 'Poor', score: 2 },
        { value: '3', label: 'Fair', score: 3 },
        { value: '4', label: 'Good', score: 4 },
        { value: '5', label: 'Excellent', score: 5 }
    ],
    PHYSICAL_ACTIVITY: [
        { value: '', label: 'Select Activity Level', score: 0 },
        { value: '1', label: 'Very Limited', score: 1 },
        { value: '2', label: 'Limited', score: 2 },
        { value: '3', label: 'Moderate', score: 3 },
        { value: '4', label: 'Active', score: 4 },
        { value: '5', label: 'Very Active', score: 5 }
    ],
    SOCIAL_FUNCTION: [
        { value: '', label: 'Select Social Function', score: 0 },
        { value: '1', label: 'Very Limited', score: 1 },
        { value: '2', label: 'Limited', score: 2 },
        { value: '3', label: 'Moderate', score: 3 },
        { value: '4', label: 'Good', score: 4 },
        { value: '5', label: 'Excellent', score: 5 }
    ],
    WORK_CAPACITY: [
        { value: '', label: 'Select Work Capacity', score: 0 },
        { value: '1', label: 'Unable to Work', score: 1 },
        { value: '2', label: 'Part-time Only', score: 2 },
        { value: '3', label: 'Full-time with Limitations', score: 3 },
        { value: '4', label: 'Full-time', score: 4 },
        { value: '5', label: 'Full-time with No Limitations', score: 5 }
    ]
};

// Clinical classification options
export const CLINICAL_CLASSIFICATIONS = {
    NYHA_CLASS: [
        { value: '', label: 'Select NYHA Class', description: '' },
        { value: 'I', label: 'Class I', description: 'No limitation of physical activity' },
        { value: 'II', label: 'Class II', description: 'Slight limitation of physical activity' },
        { value: 'III', label: 'Class III', description: 'Marked limitation of physical activity' },
        {
            value: 'IV', label: 'Class IV',
            description: 'Unable to carry on any physical activity without discomfort'
        }
    ],
    CCS_CLASS: [
        { value: '', label: 'Select CCS Class', description: '' },
        { value: 'I', label: 'Class I', description: 'Ordinary physical activity does not cause angina' },
        { value: 'II', label: 'Class II', description: 'Slight limitation of ordinary activity' },
        { value: 'III', label: 'Class III', description: 'Marked limitation of ordinary physical activity' },
        {
            value: 'IV', label: 'Class IV',
            description: 'Inability to carry on any physical activity without discomfort'
        }
    ]
};

// Reference ranges for biomarkers and vital signs
export const REFERENCE_RANGES = {
    BIOMARKERS: {
        bnp: { normal: 100, unit: 'pg/mL', name: 'BNP' },
        troponin: { normal: 14, unit: 'ng/L', name: 'Troponin I' },
        creatinine: { normal: 1.2, unit: 'mg/dL', name: 'Creatinine' },
        hemoglobin: { min: 12, max: 16, unit: 'g/dL', name: 'Hemoglobin' },
        cholesterolTotal: { normal: 200, unit: 'mg/dL', name: 'Total Cholesterol' },
        cholesterolLdl: { normal: 100, unit: 'mg/dL', name: 'LDL Cholesterol' },
        cholesterolHdl: { min: 40, unit: 'mg/dL', name: 'HDL Cholesterol' },
        triglycerides: { normal: 150, unit: 'mg/dL', name: 'Triglycerides' }
    },
    VITALS: {
        bloodPressure: {
            normal: { systolic: { max: 120 }, diastolic: { max: 80 } },
            elevated: { systolic: { max: 130 }, diastolic: { max: 80 } },
            stage1: { systolic: { max: 140 }, diastolic: { max: 90 } },
            stage2: { systolic: { min: 140 }, diastolic: { min: 90 } },
            crisis: { systolic: { min: 180 }, diastolic: { min: 120 } }
        },
        heartRate: { min: 60, max: 100, unit: 'bpm' },
        spo2: { min: 95, max: 100, unit: '%' },
        bmi: {
            underweight: { max: 18.5 },
            normal: { min: 18.5, max: 25 },
            overweight: { min: 25, max: 30 },
            obese: { min: 30 }
        }
    }
};

// Assessment field configurations
export const ASSESSMENT_FIELDS = {
    SYMPTOMS: ['anginaFrequency', 'anginaSeverity', 'shortnessOfBreath', 'fatigue',
        'chestPain', 'palpitations', 'dizziness', 'headache'],
    QUALITY_OF_LIFE: ['energyLevel', 'sleepQuality', 'physicalActivity',
        'socialFunction', 'workCapacity'],
    BIOMARKERS: ['bnp', 'troponin', 'creatinine', 'hemoglobin', 'cholesterolTotal',
        'cholesterolLdl', 'cholesterolHdl', 'triglycerides'],
    VITALS: ['height', 'weight', 'bmi', 'systolic', 'diastolic', 'heartRate', 'spo2']
};