/**
 * @file ProgressSidebar.jsx
 * Renders the progress sidebar showing evaluation completion status based on actual form data.
 */

import { CheckCircle, Clock, Circle } from 'lucide-react';
import { PROGRESS_STATUS } from '../constants/postTreatmentConstants';
import { getSectionDisplayName, getProgressBarWidth } from '../utils/evaluationUtils';

/**
 * Progress sidebar component showing real evaluation completion status
 * 
 * @param {Object} props - Component props
 * @param {Object} props.evaluation - The evaluation data object
 * @param {Object} props.progressInfo - Real-time progress information
 * @returns {JSX.Element} Progress sidebar component
 */
const ProgressSidebar = ({ evaluation, progressInfo }) => {
    /**
     * Gets the appropriate icon for a progress status
     */
    const getStatusIcon = (status) => {
        switch (status) {
            case PROGRESS_STATUS.COMPLETED:
                return <CheckCircle size={16} className="text-green-600" />;
            case PROGRESS_STATUS.IN_PROGRESS:
                return <Clock size={16} className="text-blue-600" />;
            case PROGRESS_STATUS.PENDING:
            default:
                return <Circle size={16} className="text-gray-400" />;
        }
    };

    /**
     * Gets the color classes for a progress status
     */
    const getStatusColors = (status) => {
        switch (status) {
            case PROGRESS_STATUS.COMPLETED:
                return {
                    text: 'text-green-600',
                    bg: 'bg-green-500',
                    label: 'text-green-800'
                };
            case PROGRESS_STATUS.IN_PROGRESS:
                return {
                    text: 'text-blue-600',
                    bg: 'bg-blue-500',
                    label: 'text-blue-800'
                };
            case PROGRESS_STATUS.PENDING:
            default:
                return {
                    text: 'text-gray-400',
                    bg: 'bg-gray-300',
                    label: 'text-gray-600'
                };
        }
    };

    /**
     * Gets a summary of field completion for a section
     */
    const getSectionSummary = (sectionKey, sectionData) => {
        const requiredFields = {
            vitals: ['height', 'weight', 'systolic', 'diastolic', 'heartRate', 'spo2'],
            functionalTests: ['nyhaClass', 'ccsClass', 'lvef', 'walkDistance'],
            qualityOfLife: ['energyLevel', 'sleepQuality', 'physicalActivity'],
            symptoms: ['anginaFrequency', 'anginaSeverity', 'shortnessOfBreath'],
            biomarkers: ['bnp', 'troponin', 'creatinine']
        };

        const fields = requiredFields[sectionKey] || [];
        const filledFields = fields.filter(field => {
            const value = sectionData[field];
            return value && value.toString().trim() !== '';
        });

        return `${filledFields.length}/${fields.length} fields`;
    };

    return (
        <aside className="w-80 bg-white border-l border-gray-200 overflow-y-auto no-print">
            <div className="p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-6">Evaluation Progress</h2>

                {/* Overall Progress */}
                <div className="mb-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex justify-between items-center mb-3">
                        <span className="text-sm font-medium text-blue-800">Overall Completion</span>
                        <span className="text-lg font-bold text-blue-600">{progressInfo.percentage}%</span>
                    </div>
                    <div className="w-full bg-blue-200 rounded-full h-3">
                        <div
                            className="bg-blue-600 h-3 rounded-full transition-all duration-500"
                            style={{ width: `${progressInfo.percentage}%` }}
                        ></div>
                    </div>
                    <div className="mt-2 text-xs text-blue-700">
                        {progressInfo.completedSections} of {progressInfo.totalSections} sections completed
                    </div>
                </div>

                {/* Section Progress */}
                <div className="space-y-4">
                    {Object.entries(evaluation.progress).map(([key, status]) => {
                        const colors = getStatusColors(status);
                        const sectionData = evaluation[key] || {};
                        const summary = getSectionSummary(key, sectionData);

                        return (
                            <div key={key} className="border border-gray-200 rounded-lg p-4">
                                <div className="flex items-center justify-between mb-3">
                                    <div className="flex items-center">
                                        {getStatusIcon(status)}
                                        <span className={`ml-2 font-medium text-sm ${colors.label}`}>
                                            {getSectionDisplayName(key)}
                                        </span>
                                    </div>
                                    <span className={`text-xs font-medium ${colors.text}`}>
                                        {status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')}
                                    </span>
                                </div>

                                {/* Progress bar */}
                                <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                                    <div
                                        className={`h-2 rounded-full transition-all duration-300 ${colors.bg}`}
                                        style={{ width: `${getProgressBarWidth(status)}%` }}
                                    ></div>
                                </div>

                                {/* Field summary */}
                                <div className="text-xs text-gray-500">
                                    {summary} completed
                                </div>

                                {/* Status message */}
                                <div className="mt-2 text-xs">
                                    {status === PROGRESS_STATUS.COMPLETED && (
                                        <span className="text-green-600">✓ All required fields completed</span>
                                    )}
                                    {status === PROGRESS_STATUS.IN_PROGRESS && (
                                        <span className="text-blue-600">⏳ Some fields completed</span>
                                    )}
                                    {status === PROGRESS_STATUS.PENDING && (
                                        <span className="text-gray-500">○ Not started</span>
                                    )}
                                </div>
                            </div>
                        );
                    })}
                </div>

                {/* Completion Status */}
                <div className="mt-8 p-4 rounded-lg border">
                    {progressInfo.percentage === 100 ? (
                        <div className="text-center">
                            <CheckCircle size={32} className="text-green-600 mx-auto mb-2" />
                            <p className="text-sm font-medium text-green-800">Evaluation Complete!</p>
                            <p className="text-xs text-green-600">All sections have been filled out</p>
                        </div>
                    ) : progressInfo.percentage >= 70 ? (
                        <div className="text-center">
                            <Clock size={32} className="text-blue-600 mx-auto mb-2" />
                            <p className="text-sm font-medium text-blue-800">Nearly Complete</p>
                            <p className="text-xs text-blue-600">
                                {100 - progressInfo.percentage}% remaining
                            </p>
                        </div>
                    ) : (
                        <div className="text-center">
                            <Circle size={32} className="text-gray-400 mx-auto mb-2" />
                            <p className="text-sm font-medium text-gray-600">In Progress</p>
                            <p className="text-xs text-gray-500">
                                Continue filling out the evaluation forms
                            </p>
                        </div>
                    )}
                </div>

                {/* Tips */}
                <div className="mt-6 p-3 bg-gray-50 rounded-lg border border-gray-200">
                    <h4 className="text-xs font-medium text-gray-700 mb-2">💡 Tips</h4>
                    <ul className="text-xs text-gray-600 space-y-1">
                        <li>• Fill required fields to complete each section</li>
                        <li>• Progress updates automatically as you type</li>
                        <li>• All sections should be completed for a comprehensive evaluation</li>
                    </ul>
                </div>
            </div>
        </aside>
    );
};

export default ProgressSidebar;