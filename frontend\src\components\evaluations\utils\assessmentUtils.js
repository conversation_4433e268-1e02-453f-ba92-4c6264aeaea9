/**
 * @file assessmentUtils.js
 * Utility functions for medical assessment calculations and status determinations.
 */

import { SYMPTOM_SCALES, QUALITY_SCALES, REFERENCE_RANGES, ASSESSMENT_FIELDS } from '../constants/assessmentConstants';

/**
 * Calculates BMI from height and weight.
 * 
 * @param {string|number} height - Height in centimeters
 * @param {string|number} weight - Weight in kilograms
 * @returns {string} Calculated BMI value with one decimal place, or empty string if invalid
 */
export const calculateBmi = (height, weight) => {
    const h = parseFloat(height);
    const w = parseFloat(weight);

    if (h && w && h > 0) {
        const heightInMeters = h / 100;
        const calculatedValue = w / (heightInMeters * heightInMeters);
        return calculatedValue.toFixed(1);
    }
    return '';
};

/**
 * Determines BMI status category.
 * 
 * @param {string|number} bmiValue - BMI value to categorize
 * @returns {Object|null} Object with status, message, and color class, or null if invalid
 */
export const getBmiStatus = (bmiValue) => {
    const value = parseFloat(bmiValue);
    if (!value) return null;

    if (value < 18.5) {
        return {
            status: 'underweight',
            message: 'Underweight',
            color: 'text-blue-600 bg-blue-50 border-blue-200'
        };
    }
    if (value < 25) {
        return {
            status: 'normal',
            message: 'Normal weight',
            color: 'text-green-600 bg-green-50 border-green-200'
        };
    }
    if (value < 30) {
        return {
            status: 'overweight',
            message: 'Overweight',
            color: 'text-yellow-600 bg-yellow-50 border-yellow-200'
        };
    }
    return {
        status: 'obese',
        message: 'Obese',
        color: 'text-red-600 bg-red-50 border-red-200'
    };
};

/**
 * Determines blood pressure category based on systolic and diastolic values.
 * 
 * @param {string|number} systolic - Systolic blood pressure
 * @param {string|number} diastolic - Diastolic blood pressure
 * @returns {Object|null} Object with category, message, and color class, or null if invalid
 */
export const getBloodPressureStatus = (systolic, diastolic) => {
    if (!systolic || !diastolic) return null;

    const sys = parseFloat(systolic);
    const dia = parseFloat(diastolic);

    if (sys < 120 && dia < 80) {
        return {
            category: 'Normal',
            message: 'Optimal blood pressure',
            colorClass: 'text-green-600 bg-green-50 border-green-200'
        };
    } else if (sys < 130 && dia < 80) {
        return {
            category: 'Elevated',
            message: 'Elevated blood pressure',
            colorClass: 'text-yellow-600 bg-yellow-50 border-yellow-200'
        };
    } else if ((sys >= 130 && sys < 140) || (dia >= 80 && dia < 90)) {
        return {
            category: 'Stage 1 Hypertension',
            message: 'High blood pressure - Stage 1',
            colorClass: 'text-orange-600 bg-orange-50 border-orange-200'
        };
    } else if (sys >= 140 || dia >= 90) {
        return {
            category: 'Stage 2 Hypertension',
            message: 'High blood pressure - Stage 2',
            colorClass: 'text-red-600 bg-red-50 border-red-200'
        };
    } else if (sys >= 180 || dia >= 120) {
        return {
            category: 'Hypertensive Crisis',
            message: 'Seek immediate medical attention',
            colorClass: 'text-red-700 bg-red-100 border-red-300'
        };
    }
    return null;
};

/**
 * Calculates overall symptom burden percentage.
 * 
 * @param {Object} symptoms - Object containing symptom assessment values
 * @returns {Object} Object with burden metrics including score, percentage, and counts
 */
export const calculateSymptomBurden = (symptoms) => {
    let totalScore = 0;
    let maxPossibleScore = 0;
    let symptomsAssessed = 0;

    ASSESSMENT_FIELDS.SYMPTOMS.forEach(field => {
        const value = symptoms[field];
        if (value && value !== '') {
            const isFrequency = field === 'anginaFrequency';
            const scale = isFrequency ? SYMPTOM_SCALES.FREQUENCY : SYMPTOM_SCALES.SEVERITY;
            const option = scale.find(opt => opt.value === value);

            if (option) {
                totalScore += option.score;
                maxPossibleScore += isFrequency ? 5 : 4;
                symptomsAssessed++;
            }
        }
    });

    if (symptomsAssessed === 0) {
        return {
            burden: 0,
            percentage: 0,
            assessed: 0,
            total: ASSESSMENT_FIELDS.SYMPTOMS.length
        };
    }

    const burdenPercentage = Math.round((totalScore / maxPossibleScore) * 100);

    return {
        burden: totalScore,
        maxBurden: maxPossibleScore,
        percentage: burdenPercentage,
        assessed: symptomsAssessed,
        total: ASSESSMENT_FIELDS.SYMPTOMS.length
    };
};

/**
 * Calculates overall quality of life score.
 * 
 * @param {Object} qualityOfLife - Object containing quality of life assessment values
 * @returns {Object} Object with score metrics including percentage and field counts
 */
export const calculateQualityOfLifeScore = (qualityOfLife) => {
    const fields = ASSESSMENT_FIELDS.QUALITY_OF_LIFE;
    const scales = [
        QUALITY_SCALES.ENERGY_LEVEL,
        QUALITY_SCALES.SLEEP_QUALITY,
        QUALITY_SCALES.PHYSICAL_ACTIVITY,
        QUALITY_SCALES.SOCIAL_FUNCTION,
        QUALITY_SCALES.WORK_CAPACITY
    ];

    let totalScore = 0;
    let validFields = 0;

    fields.forEach((field, index) => {
        const value = qualityOfLife[field];
        if (value) {
            const option = scales[index].find(opt => opt.value === value);
            if (option) {
                totalScore += option.score;
                validFields++;
            }
        }
    });

    if (validFields === 0) {
        return {
            score: 0,
            percentage: 0,
            validFields: 0,
            totalFields: fields.length
        };
    }

    const maxPossibleScore = validFields * 5;
    const percentage = Math.round((totalScore / maxPossibleScore) * 100);

    return {
        score: totalScore,
        maxScore: maxPossibleScore,
        percentage,
        validFields,
        totalFields: fields.length
    };
};

/**
 * Evaluates biomarker values against reference ranges.
 * 
 * @param {Object} biomarkers - Object containing biomarker values
 * @returns {Object} Object with biomarker evaluation metrics
 */
export const evaluateBiomarkers = (biomarkers) => {
    const biomarkerFields = ASSESSMENT_FIELDS.BIOMARKERS;

    const filledBiomarkers = biomarkerFields.filter(field => {
        const value = biomarkers[field];
        return value && value.toString().trim() !== '';
    });

    const evaluatedBiomarkers = biomarkerFields.filter(field => {
        const value = biomarkers[field];
        if (!value || value.toString().trim() === '') return false;

        const numValue = parseFloat(value);
        const range = REFERENCE_RANGES.BIOMARKERS[field];

        if (range.min && range.max) {
            return numValue >= range.min && numValue <= range.max;
        } else if (range.min) {
            return numValue >= range.min;
        } else if (range.normal) {
            return numValue <= range.normal;
        }
        return false;
    });

    const normalPercentage = filledBiomarkers.length > 0 ?
        Math.round((evaluatedBiomarkers.length / filledBiomarkers.length) * 100) : 0;

    return {
        totalMeasured: filledBiomarkers.length,
        withinNormalRange: evaluatedBiomarkers.length,
        normalPercentage
    };
};

/**
 * Determines angina burden assessment based on frequency and severity.
 * 
 * @param {string} anginaFrequency - Angina frequency value
 * @param {string} anginaSeverity - Angina severity value
 * @returns {Object|null} Object with angina assessment details, or null if no data
 */
export const getAnginaAssessment = (anginaFrequency, anginaSeverity) => {
    if (!anginaFrequency && !anginaSeverity) return null;

    const freqScore = anginaFrequency ?
        SYMPTOM_SCALES.FREQUENCY.find(opt => opt.value === anginaFrequency)?.score || 0 : 0;
    const sevScore = anginaSeverity ?
        SYMPTOM_SCALES.SEVERITY.find(opt => opt.value === anginaSeverity)?.score || 0 : 0;

    const anginaBurden = Math.max(freqScore, sevScore);

    const assessments = {
        0: {
            assessment: 'No angina symptoms',
            ccsClass: 'Asymptomatic',
            color: 'text-green-600 bg-green-50 border-green-200'
        },
        1: {
            assessment: 'Minimal angina burden',
            ccsClass: 'Approximate CCS Class I',
            color: 'text-blue-600 bg-blue-50 border-blue-200'
        },
        2: {
            assessment: 'Moderate angina burden',
            ccsClass: 'Approximate CCS Class II',
            color: 'text-yellow-600 bg-yellow-50 border-yellow-200'
        },
        3: {
            assessment: 'Significant angina burden',
            ccsClass: 'Approximate CCS Class III',
            color: 'text-orange-600 bg-orange-50 border-orange-200'
        }
    };

    const result = anginaBurden <= 3 ?
        assessments[anginaBurden] :
        {
            assessment: 'Severe angina burden',
            ccsClass: 'Approximate CCS Class IV',
            color: 'text-red-600 bg-red-50 border-red-200'
        };

    return { ...result, burden: anginaBurden };
};

/**
 * Gets appropriate recommendations based on assessment scores.
 * 
 * @param {string} assessmentType - Type of assessment ('symptoms', 'quality', 'biomarkers')
 * @param {number} score - Assessment score or percentage
 * @param {Object} additionalData - Additional data for specific recommendations
 * @returns {Array} Array of recommendation strings
 */
export const getRecommendations = (assessmentType, score, additionalData = {}) => {
    const recommendations = [];

    switch (assessmentType) {
        case 'symptoms':
            if (score === 0) {
                recommendations.push('Excellent symptom control');
            } else if (score <= 25) {
                recommendations.push('Good symptom control');
                recommendations.push('Continue current management');
            } else if (score <= 50) {
                recommendations.push('Consider symptom optimization');
                recommendations.push('Review medication adherence');
            } else if (score <= 75) {
                recommendations.push('Requires symptom management review');
                recommendations.push('Consider specialist consultation');
            } else {
                recommendations.push('Urgent symptom management needed');
                recommendations.push('Consider hospital evaluation');
            }
            break;

        case 'quality':
            if (score >= 80) {
                recommendations.push('Maintain current lifestyle and activities');
            } else if (score >= 60) {
                recommendations.push('Consider minor lifestyle improvements');
            } else if (score >= 40) {
                recommendations.push('Focus on improving limited areas');
                recommendations.push('Consider lifestyle modifications');
            } else {
                recommendations.push('Comprehensive quality of life intervention needed');
                recommendations.push('Consider counseling or support services');
            }
            break;

        case 'biomarkers':
            if (score < 50) {
                recommendations.push('Review abnormal biomarker values');
                recommendations.push('Consider medication adjustments');
            } else if (score < 80) {
                recommendations.push('Monitor trending biomarker values');
            } else {
                recommendations.push('Good biomarker control');
            }
            break;

        default:
            break;
    }

    return recommendations;
};