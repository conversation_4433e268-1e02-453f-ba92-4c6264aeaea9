/**
 * Component for displaying patient information and clinical data.
 */

import { FaUserInjured } from 'react-icons/fa';

const CLINICAL_THRESHOLDS = {
    LVEF_CRITICAL: 30,
    BNP_ELEVATED: 400
};

/**
 * Displays patient information in a card format.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.patient - Patient data object
 * @returns {JSX.Element} Patient information card
 */
const PatientInformationCard = ({ patient }) => {
    return (
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
                <div>
                    <h2 className="text-lg font-medium text-gray-900">Patient Information</h2>
                    <p className="mt-1 text-sm text-gray-500">Personal and medical details</p>
                </div>
                <div className="flex-shrink-0 h-12 w-12 flex items-center justify-center rounded-full bg-blue-100 text-primary">
                    <FaUserInjured className="h-6 w-6" />
                </div>
            </div>
            <div className="border-t border-gray-200 px-4 py-5 sm:p-0">
                <dl className="sm:divide-y sm:divide-gray-200">
                    <div className="py-3 sm:py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">Full name</dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{patient.name}</dd>
                    </div>
                    <div className="py-3 sm:py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">Age / Gender</dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{patient.age} years / {patient.gender}</dd>
                    </div>
                    <div className="py-3 sm:py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">Diagnosis</dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{patient.diagnosis}</dd>
                    </div>
                    <div className="py-3 sm:py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">LVEF</dt>
                        <dd className={`mt-1 text-sm sm:mt-0 sm:col-span-2 ${patient.lvef < CLINICAL_THRESHOLDS.LVEF_CRITICAL ? 'text-red-600 font-bold' : 'text-gray-900'}`}>
                            {patient.lvef}%
                        </dd>
                    </div>
                    <div className="py-3 sm:py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">BNP</dt>
                        <dd className={`mt-1 text-sm sm:mt-0 sm:col-span-2 ${patient.bnp > CLINICAL_THRESHOLDS.BNP_ELEVATED ? 'text-red-600 font-bold' : 'text-gray-900'}`}>
                            {patient.bnp} pg/mL
                        </dd>
                    </div>
                </dl>
            </div>
        </div>
    );
};

export default PatientInformationCard;
