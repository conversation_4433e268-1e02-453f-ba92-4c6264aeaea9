/**
 * @file usePatientEvaluation.js
 * Custom hook for managing patient evaluation data and session completion
 */

import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { localStorageService } from '../../../services/localStorageService';
import { calculateSessionCompletion } from '../utils/sessionUtils';
import { populateEvaluationFromPatient } from '../utils/evaluationUtils';
import { INITIAL_EVALUATION_STATE, DEFAULT_TOTAL_SESSIONS } from '../constants/postTreatmentConstants';

/**
 * Custom hook for managing patient evaluation data
 * 
 * @param {string} patientId - Patient ID from URL parameters
 * @returns {Object} Patient data, evaluation state, and loading state
 */
export const usePatientEvaluation = (patientId) => {
    const navigate = useNavigate();

    const [patient, setPatient] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [evaluation, setEvaluation] = useState(INITIAL_EVALUATION_STATE);
    const [sessionCompletion, setSessionCompletion] = useState({
        completed: 0,
        total: DEFAULT_TOTAL_SESSIONS,
        isEligibleForEvaluation: false
    });

    useEffect(() => {
        const fetchPatientData = async () => {
            try {
                setIsLoading(true);

                // Retrieve patient data from localStorage
                const foundPatient = localStorageService.getPatientById(patientId);

                if (foundPatient) {
                    setPatient(foundPatient);

                    // Calculate real-time session completion
                    const sessionStats = calculateSessionCompletion(foundPatient);
                    setSessionCompletion(sessionStats);

                    // Show warning if patient is not eligible for evaluation yet
                    if (!sessionStats.isEligibleForEvaluation) {
                        console.warn(
                            `Patient has only completed ${sessionStats.completed}/${sessionStats.total} sessions. ` +
                            `Post-treatment evaluation recommended after completing at least 90% of treatment.`
                        );
                    }

                    // Populate evaluation with existing outcome measures if available
                    const populatedEvaluation = populateEvaluationFromPatient(
                        INITIAL_EVALUATION_STATE,
                        foundPatient
                    );
                    setEvaluation(populatedEvaluation);

                } else {
                    console.error('Patient not found');
                    navigate('/patients');
                }

                setIsLoading(false);
            } catch (error) {
                console.error('Error fetching patient data:', error);
                setIsLoading(false);
            }
        };

        if (patientId) {
            fetchPatientData();
        }
    }, [patientId, navigate]);

    return {
        patient,
        isLoading,
        evaluation,
        setEvaluation,
        sessionCompletion,
        setSessionCompletion
    };
};