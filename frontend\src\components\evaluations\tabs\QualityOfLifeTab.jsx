/**
 * @file QualityOfLifeTab.jsx
 * Component for displaying and editing quality of life assessments in post-treatment evaluation.
 * Handles physical well-being, emotional health, social function, and work capacity metrics.
 */

import { Heart, Brain, Briefcase, TrendingUp, CheckCircle, AlertTriangle, Info } from 'lucide-react';

import { CARD_STYLES } from '../constants/commonStyles';
import { QUALITY_SCALES } from '../constants/assessmentConstants';

import {
  AssessmentSelect,
  SummaryCard,
  ClinicalNotes,
  SectionHeader
} from '../evaluation-components/commonComponents';
import { calculateQualityOfLifeScore, getRecommendations } from '../utils/assessmentUtils';


/**
 * Status indicator component for quality of life metrics
 */
const QualityStatus = ({ value, scale, label }) => {
  if (!value) return null;

  const option = scale.find(opt => opt.value === value);
  if (!option) return null;

  let status = '';
  let description = '';
  let colorClass = '';
  let Icon = Info;

  const score = option.score;

  if (score >= 5) {
    status = 'Excellent';
    description = 'Optimal quality of life in this area';
    colorClass = 'text-green-600 bg-green-50 border-green-200';
    Icon = CheckCircle;
  } else if (score >= 4) {
    status = 'Good';
    description = 'Good quality of life with minimal limitations';
    colorClass = 'text-blue-600 bg-blue-50 border-blue-200';
    Icon = TrendingUp;
  } else if (score >= 3) {
    status = 'Moderate';
    description = 'Fair quality of life with some limitations';
    colorClass = 'text-yellow-600 bg-yellow-50 border-yellow-200';
    Icon = Info;
  } else if (score >= 2) {
    status = 'Poor';
    description = 'Significant limitations affecting quality of life';
    colorClass = 'text-orange-600 bg-orange-50 border-orange-200';
    Icon = AlertTriangle;
  } else {
    status = 'Very Poor';
    description = 'Severe limitations significantly impacting quality of life';
    colorClass = 'text-red-600 bg-red-50 border-red-200';
    Icon = AlertTriangle;
  }

  return (
    <div className={`p-3 rounded-lg border ${colorClass} mt-2`}>
      <div className="flex items-center">
        <Icon size={16} className="mr-2" />
        <div>
          <span className="text-sm font-medium">
            {label}: {status}
          </span>
          <div className="text-xs opacity-75">{description}</div>
        </div>
      </div>
    </div>
  );
};

/**
 * Quality of life summary component
 */
const QualityOfLifeSummary = ({ qualityOfLife }) => {
  const scoreData = calculateQualityOfLifeScore(qualityOfLife);

  if (scoreData.validFields === 0) {
    return (
      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
        <h4 className="text-lg font-medium text-gray-800 mb-2">Quality of Life Summary</h4>
        <p className="text-gray-600">Complete assessments to see quality of life summary</p>
      </div>
    );
  }


  // Determine overall quality of life status
  let overallStatus = '';
  let statusColor = '';
  let Icon = Info;

  if (scoreData.percentage >= 80) {
    overallStatus = 'Excellent';
    statusColor = 'text-green-600';
    Icon = CheckCircle;
  } else if (scoreData.percentage >= 60) {
    overallStatus = 'Good';
    statusColor = 'text-blue-600';
    Icon = TrendingUp;
  } else if (scoreData.percentage >= 40) {
    overallStatus = 'Fair';
    statusColor = 'text-yellow-600';
    Icon = AlertTriangle;
  } else {
    overallStatus = 'Poor';
    statusColor = 'text-red-600';
    Icon = AlertTriangle;
  }

  // Get base recommendations from utility
  const recommendations = getRecommendations('quality', scoreData.percentage);

  // Add specific recommendations based on low scores
  const specificRecommendations = [...recommendations];
  if (qualityOfLife.energyLevel && parseInt(qualityOfLife.energyLevel) <= 2) {
    specificRecommendations.push('Address energy levels through medical evaluation');
  }
  if (qualityOfLife.sleepQuality && parseInt(qualityOfLife.sleepQuality) <= 2) {
    specificRecommendations.push('Sleep study or sleep hygiene counseling');
  }
  if (qualityOfLife.physicalActivity && parseInt(qualityOfLife.physicalActivity) <= 2) {
    specificRecommendations.push('Consider cardiac rehabilitation program');
  }
  if (qualityOfLife.socialFunction && parseInt(qualityOfLife.socialFunction) <= 2) {
    specificRecommendations.push('Social support services evaluation');
  }
  if (qualityOfLife.workCapacity && parseInt(qualityOfLife.workCapacity) <= 2) {
    specificRecommendations.push('Occupational therapy assessment');
  }


  return (
    <SummaryCard
      title="Quality of Life Summary"
      icon={Icon}
      metrics={[
        {
          label: 'Overall Score',
          value: `${scoreData.percentage}%`,
          color: statusColor
        },
        {
          label: 'Domains Assessed',
          value: `${scoreData.validFields}/${scoreData.totalFields}`,
          color: 'text-blue-600'
        },
        {
          label: overallStatus,
          value: '',
          color: statusColor
        }
      ]}
      recommendations={specificRecommendations}
    />
  );
};

/**
 * Quality of Life tab component for post-treatment evaluation
 */
const QualityOfLifeTab = ({ qualityOfLife, onChange }) => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <SectionHeader
        title="Quality of Life Assessment"
        icon={Heart}
        iconColor="text-pink-600"
      />


      {/* Physical Well-being */}
      <div className={CARD_STYLES.SECTION}>
        <h4 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
          <Heart className="mr-2 text-red-500" size={20} />
          Physical Well-being
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <AssessmentSelect
            label="Energy Level"
            value={qualityOfLife.energyLevel}
            onChange={onChange}
            fieldName="energyLevel"
            options={QUALITY_SCALES.ENERGY_LEVEL}
            description="How would you rate your overall energy level?"
          />
          <AssessmentSelect
            label="Sleep Quality"
            value={qualityOfLife.sleepQuality}
            onChange={onChange}
            fieldName="sleepQuality"
            options={QUALITY_SCALES.SLEEP_QUALITY}
            description="How would you rate your sleep quality?"
          />
        </div>

        {/* Physical well-being status indicators */}
        <QualityStatus value={qualityOfLife.energyLevel} scale={QUALITY_SCALES.ENERGY_LEVEL} label="Energy Level" />
        <QualityStatus value={qualityOfLife.sleepQuality} scale={QUALITY_SCALES.SLEEP_QUALITY} label="Sleep Quality" />
      </div>

      {/* Activity & Function */}
      <div className={CARD_STYLES.SECTION}>
        <h4 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
          <Brain className="mr-2 text-purple-500" size={20} />
          Activity & Function
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <AssessmentSelect
            label="Physical Activity Level"
            value={qualityOfLife.physicalActivity}
            onChange={onChange}
            fieldName="physicalActivity"
            options={QUALITY_SCALES.PHYSICAL_ACTIVITY}
            description="How active are you in daily life?"
          />
          <AssessmentSelect
            label="Social Function"
            value={qualityOfLife.socialFunction}
            onChange={onChange}
            fieldName="socialFunction"
            options={QUALITY_SCALES.SOCIAL_FUNCTION}
            description="How well can you participate in social activities?"
          />
        </div>

        {/* Activity & function status indicators */}
        <QualityStatus value={qualityOfLife.physicalActivity} scale={QUALITY_SCALES.PHYSICAL_ACTIVITY} label="Physical Activity" />
        <QualityStatus value={qualityOfLife.socialFunction} scale={QUALITY_SCALES.SOCIAL_FUNCTION} label="Social Function" />
      </div>

      {/* Work & Productivity */}
      <div className={CARD_STYLES.SECTION}>
        <h4 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
          <Briefcase className="mr-2 text-blue-500" size={20} />
          Work & Productivity
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <AssessmentSelect
            label="Work Capacity"
            value={qualityOfLife.workCapacity}
            onChange={onChange}
            fieldName="workCapacity"
            options={QUALITY_SCALES.WORK_CAPACITY}
            description="What is your current work capacity?"
          />
        </div>

        {/* Work capacity status indicator */}
        <QualityStatus value={qualityOfLife.workCapacity} scale={QUALITY_SCALES.WORK_CAPACITY} label="Work Capacity" />
      </div>

      {/* Quality of Life Summary */}
      <QualityOfLifeSummary qualityOfLife={qualityOfLife} />

      {/* Clinical Notes */}
      <ClinicalNotes
        value={qualityOfLife.notes}
        onChange={onChange}
        placeholder="Enter clinical observations, patient-reported outcomes, quality of life concerns, or any relevant psychosocial information..."
      />
    </div>
  );
};

export default QualityOfLifeTab;