/**
 *@file commonComponents.jsx
 * Reusable UI components for medical evaluation forms.
 */

import React from "react";
import {
  Check<PERSON>ir<PERSON>,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
} from "lucide-react";
import { INPUT_STYLES, STATUS_COLORS } from "../constants/commonStyles";

/**
 * Reusable input field component for assessments.
 *
 * @param {Object} props
 * @param {string} props.label - Label for the input field
 * @param {string} props.value - Current value of the input
 * @param {Function} props.onChange - Change handler function
 * @param {string} props.fieldName - Field name for the onChange handler
 * @param {string} props.placeholder - Placeholder text
 * @param {string} props.unit - Unit to display (optional)
 * @param {string} props.type - Input type (default: "number")
 * @param {string} props.description - Help text (optional)
 * @param {string|number} props.min - Minimum value (optional)
 * @param {string|number} props.max - Maximum value (optional)
 */
export const AssessmentInput = ({
  label,
  value,
  onChange,
  fieldName,
  placeholder,
  unit,
  type = "number",
  description,
  min,
  max,
}) => (
  <div className="space-y-2">
    <label className={INPUT_STYLES.LABEL}>{label}</label>
    <div className="relative">
      <input
        type={type}
        value={value}
        onChange={(e) => onChange(fieldName, e.target.value)}
        className={INPUT_STYLES.FIELD}
        placeholder={placeholder}
        min={min}
        max={max}
        step="0.1"
      />
      {unit && (
        <span className="absolute right-3 top-3 text-sm text-gray-500">
          {unit}
        </span>
      )}
    </div>
    {description && <p className="text-xs text-gray-500">{description}</p>}
  </div>
);

/**
 * Reusable select field component for assessments.
 *
 * @param {Object} props
 * @param {string} props.label - Label for the select field
 * @param {string} props.value - Current selected value
 * @param {Function} props.onChange - Change handler function
 * @param {string} props.fieldName - Field name for the onChange handler
 * @param {Array} props.options - Array of option objects with value and label
 * @param {string} props.description - Help text (optional)
 */
export const AssessmentSelect = ({
  label,
  value,
  onChange,
  fieldName,
  options,
  description,
}) => (
  <div className="space-y-2">
    <label className={INPUT_STYLES.LABEL}>{label}</label>
    <select
      value={value}
      onChange={(e) => onChange(fieldName, e.target.value)}
      className={INPUT_STYLES.SELECT}
    >
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
    {description && <p className="text-xs text-gray-500">{description}</p>}
  </div>
);

/**
 * Status indicator component for displaying assessment results.
 *
 * @param {Object} props
 * @param {string} props.value - Assessment value
 * @param {Object} props.range - Reference range object with thresholds
 * @param {string} props.label - Label for the assessment
 * @param {string} props.unit - Unit of measurement (optional)
 * @param {string} props.fieldName - Field name for specific logic (optional)
 */
export const StatusIndicator = ({
  value,
  range,
  label,
  unit = "",
  fieldName,
}) => {
  if (!value || isNaN(parseFloat(value))) return null;

  const numValue = parseFloat(value);
  let status = "normal";
  let message = "Normal";
  let colorClass = STATUS_COLORS.NORMAL;
  let Icon = CheckCircle;

  // Determine status based on ranges
  if (range) {
    if (range.min && range.max) {
      // Range with both min and max
      if (numValue < range.min) {
        status = "low";
        message = `Below normal range (${range.min}-${range.max} ${
          range.unit || unit
        })`;
        colorClass = STATUS_COLORS.LOW;
        Icon = TrendingDown;
      } else if (numValue > range.max) {
        status = "high";
        message = `Above normal range (${range.min}-${range.max} ${
          range.unit || unit
        })`;
        colorClass = STATUS_COLORS.ELEVATED;
        Icon = TrendingUp;
      } else {
        message = `Normal (${range.min}-${range.max} ${range.unit || unit})`;
      }
    } else if (range.min) {
      // Only minimum value
      if (numValue < range.min) {
        status = "low";
        message = `Below target (should be ≥ ${range.min} ${
          range.unit || unit
        })`;
        colorClass = STATUS_COLORS.ELEVATED;
        Icon = TrendingDown;
      } else {
        message = `Good (≥ ${range.min} ${range.unit || unit})`;
      }
    } else if (range.normal) {
      // Only maximum value
      if (numValue > range.normal) {
        status = "high";
        message = `Elevated (normal < ${range.normal} ${range.unit || unit})`;
        colorClass = STATUS_COLORS.ELEVATED;
        Icon = TrendingUp;
      } else {
        message = `Normal (< ${range.normal} ${range.unit || unit})`;
      }
    }
  }

  // Special handling for specific biomarkers
  if (fieldName === "bnp" && numValue > range?.normal) {
    if (numValue > 300) {
      message = `Severely elevated - suggests heart failure (normal < ${range.normal} ${range.unit})`;
      Icon = AlertTriangle;
    } else {
      message = `Elevated - may indicate heart stress (normal < ${range.normal} ${range.unit})`;
    }
  }

  if (fieldName === "troponin" && numValue > range?.normal) {
    message = `Elevated - may indicate cardiac injury (normal < ${range.normal} ${range.unit})`;
    Icon = AlertTriangle;
  }

  return (
    <div className={`p-3 rounded-lg border ${colorClass} mt-2`}>
      <div className="flex items-center">
        <Icon size={16} className="mr-2" />
        <div>
          <span className="text-sm font-medium">
            {label}: {value} {unit}
          </span>
          <div className="text-xs opacity-75">{message}</div>
        </div>
      </div>
    </div>
  );
};

/**
 * Assessment card component for displaying key metrics.
 *
 * @param {Object} props
 * @param {string} props.title - Card title
 * @param {string} props.value - Primary value to display
 * @param {string} props.status - Status description
 * @param {string} props.description - Additional description (optional)
 * @param {React.Component} props.icon - Icon component
 * @param {string} props.colorClass - CSS color classes
 */
export const AssessmentCard = ({
  title,
  value,
  status,
  description,
  icon: Icon,
  colorClass,
}) => (
  <div className={`p-4 border-2 rounded-lg ${colorClass}`}>
    <div className="flex items-center justify-between mb-2">
      <div className="flex items-center">
        <Icon size={20} className="mr-2" />
        <h5 className="font-medium">{title}</h5>
      </div>
      <div className="text-right">
        <span className="text-lg font-bold">{value || "—"}</span>
      </div>
    </div>
    <div className="text-sm opacity-75">
      <strong>{status}</strong>
    </div>
    {description && <p className="text-xs mt-1 opacity-75">{description}</p>}
  </div>
);

/**
 * Summary card component for displaying overall assessment results.
 *
 * @param {Object} props
 * @param {string} props.title - Summary title
 * @param {Array} props.metrics - Array of metric objects with label, value, and color
 * @param {Array} props.recommendations - Array of recommendation strings (optional)
 * @param {React.Component} props.icon - Icon component (optional)
 */
export const SummaryCard = ({
  title,
  metrics,
  recommendations,
  icon: Icon,
}) => (
  <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
    <div className="flex items-center mb-4">
      {Icon && <Icon size={20} className="mr-2 text-blue-800" />}
      <h4 className="text-lg font-medium text-blue-800">{title}</h4>
    </div>

    <div className={`grid grid-cols-${metrics.length} gap-4 mb-4`}>
      {metrics.map((metric, index) => (
        <div key={index} className="text-center">
          <div
            className={`text-2xl font-bold ${metric.color || "text-blue-600"}`}
          >
            {metric.value}
          </div>
          <div className="text-xs text-blue-700">{metric.label}</div>
        </div>
      ))}
    </div>

    {recommendations && recommendations.length > 0 && (
      <div>
        <div className="text-sm text-blue-700 mb-2">
          Clinical Recommendations:
        </div>
        <ul className="text-xs text-blue-600 space-y-1">
          {recommendations.map((rec, index) => (
            <li key={index}>• {rec}</li>
          ))}
        </ul>
      </div>
    )}
  </div>
);

/**
 * Clinical notes textarea component.
 *
 * @param {Object} props
 * @param {string} props.value - Current notes value
 * @param {Function} props.onChange - Change handler function
 * @param {string} props.placeholder - Placeholder text
 * @param {string} props.title - Section title (optional)
 */
export const ClinicalNotes = ({
  value,
  onChange,
  placeholder,
  title = "Clinical Assessment Notes",
}) => (
  <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
    <h4 className="text-lg font-medium text-blue-800 mb-3">{title}</h4>
    <textarea
      value={value || ""}
      onChange={(e) => onChange("notes", e.target.value)}
      className={INPUT_STYLES.TEXTAREA}
      placeholder={placeholder}
    />
  </div>
);

/**
 * Section header component with icon.
 *
 * @param {Object} props
 * @param {string} props.title - Section title
 * @param {React.Component} props.icon - Icon component
 * @param {string} props.iconColor - Icon color class (optional)
 */
export const SectionHeader = ({
  title,
  icon: Icon,
  iconColor = "text-blue-600",
}) => (
  <div className="flex items-center space-x-2">
    <Icon className={iconColor} size={24} />
    <h3 className="text-xl font-semibold text-gray-900">{title}</h3>
  </div>
);
