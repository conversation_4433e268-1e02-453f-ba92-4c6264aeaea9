import { VITALS_CHART_PROPERTIES } from "./commonStyles";

// Constants
export const EVALUATION_TABS = {
  VITALS: "vitals",
  FUNCTIONAL_TESTS: "functional-tests",
  QUALITY_OF_LIFE: "qol",
  SYMPTOMS: "symptoms",
  BIOMARKERS: "biomarkers",
};

export const PROGRESS_STATUS = {
  COMPLETED: "completed",
  IN_PROGRESS: "in_progress",
  PENDING: "pending",
};

export const DEFAULT_TOTAL_SESSIONS = 35;

export const SESSION_STATUS = {
  COMPLETED: "completed",
  SCHEDULED: "scheduled",
  IN_PROGRESS: "in-progress",
  CANCELLED: "cancelled",
};

export const INITIAL_EVALUATION_STATE = {
  vitals: {
    height: "",
    weight: "",
    bmi: "",
    systolic: "",
    diastolic: "",
    heartRate: "",
    spo2: "",
    notes: "",
  },
  functionalTests: {
    nyhaClass: "",
    ccsClass: "",
    lvef: "",
    walkDistance: "",
    notes: "",
  },
  qualityOfLife: {
    energyLevel: "",
    sleepQuality: "",
    physicalActivity: "",
    socialFunction: "",
    workCapacity: "",
    notes: "",
  },
  symptoms: {
    anginaFrequency: "",
    anginaSeverity: "",
    shortnessOfBreath: "",
    fatigue: "",
    chestPain: "",
    dizziness: "",
    palpitations: "",
    headache: "",
    notes: "",
  },
  biomarkers: {
    bnp: "",
    troponin: "",
    creatinine: "",
    hemoglobin: "",
    cholesterolTotal: "",
    cholesterolLdl: "",
    cholesterolHdl: "",
    triglycerides: "",
    notes: "",
  },
  progress: {
    vitals: PROGRESS_STATUS.PENDING,
    functionalTests: PROGRESS_STATUS.PENDING,
    qualityOfLife: PROGRESS_STATUS.PENDING,
    symptoms: PROGRESS_STATUS.PENDING,
    biomarkers: PROGRESS_STATUS.PENDING,
  },
};

export const VITALS_BARCHART_OPTIONS = {
  responsive: true,
  plugins: {
    legend: {
      position: "top",
    },
    title: {
      display: true,
      text: "Vitals Comparison",
    },
  },
};
export const VITALS_CHART_LABELS = [
  "BMI",
  "Heart Rate",
  "Systolic BP",
  "Diastolic BP",
  "SpO2",
];
export const DEFAULT_PRE_SESSION_VITALS = {
  bmi: 0,
  heartRate: 0,
  systolic: 0,
  diastolic: 0,
  spo2: 0,
};

export const BARCHART_DEFAULT_DATA = {
  labels: VITALS_CHART_LABELS,
  datasets: [
    {
      label: VITALS_CHART_PROPERTIES.before.label,

      data: [0, 0, 0, 0],
      backgroundColor: VITALS_CHART_PROPERTIES.before.backgroundColor,
    },
    {
      label: VITALS_CHART_PROPERTIES.after.label,
      data: [0, 0, 0, 0],
      backgroundColor: VITALS_CHART_PROPERTIES.after.backgroundColor,
    },
  ],
};
