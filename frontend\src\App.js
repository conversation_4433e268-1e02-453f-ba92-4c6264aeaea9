/**
 * App.js
 * Main application entry point for Syncore frontend.
 * Handles routing, authentication context, and sets a default doctor ID for testing.
 */

import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';

// Layout
import Layout from './components/layout/Layout';

// Auth Components
import Login from './components/auth/Login';
import Signup from './components/auth/Signup';
import PrivateRoute from './components/auth/PrivateRoute';

// Dashboard Components
import DoctorDashboard from './components/dashboard/DoctorDashboard';
import NurseDashboard from './components/dashboard/NurseDashboard';
import AdminDashboard from './components/dashboard/AdminDashboard';

// Session Components
import SessionExecutionPanel from './components/sessions/SessionExecutionPanel';
import NewSessionPage from './components/sessions/NewSessionPage';

// Patient Components
import PatientIntakeWizard from './components/patients/PatientIntakeWizard';
import PatientDetailsScreen from './components/patients/PatientDetailsScreen';
import PatientsPage from './components/patients/PatientsPage';

// Evaluation Components
import PostTreatmentEvaluation from './components/evaluations/PostTreatmentEvaluation';

// Appointment Components
import AppointmentsPage from './components/appointments/AppointmentsPage';

/**
 * Main App component.
 * Sets a default doctorId for testing and defines all application routes.
 * @returns {JSX.Element} The root application component.
 */
function App() {
  useEffect(() => {
    // Always set the doctor ID for testing (overwrites any previous value)
    localStorage.setItem('syncore_doctorId', '0197affd-c0a5-726e-9b76-fa5ba58164e4');
  }, []);

  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* Public Routes */}
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<Signup />} />
          {/* Protected Routes */}
          <Route element={<Layout />}>
            {/* Default redirect */}
            <Route path="/" element={<Navigate to="/login" replace />} />

            {/* Doctor Routes */}
            <Route element={<PrivateRoute requiredRole="doctor" />}>
              <Route path="/doctor-dashboard" element={<DoctorDashboard />} />
            </Route>

            {/* Nurse Routes */}
            <Route element={<PrivateRoute requiredRole="nurse" />}>
              <Route path="/nurse-dashboard" element={<NurseDashboard />} />
              <Route path="/sessions/:sessionId" element={<SessionExecutionPanel />} />
            </Route>

            {/* Admin Routes */}
            <Route element={<PrivateRoute requiredRole="admin" />}>
              <Route path="/admin-dashboard" element={<AdminDashboard />} />
            </Route>

            {/* Shared Routes (accessible by any authenticated user) */}
            <Route element={<PrivateRoute />}>
              <Route path="/patients" element={<PatientsPage />} />
              <Route path="/patients/new" element={<PatientIntakeWizard />} />
              <Route path="/patients/:patientId" element={<PatientDetailsScreen />} />
              <Route path="/appointments" element={<AppointmentsPage />} />
              <Route path="/appointments/new" element={<AppointmentsPage />} />
              <Route path="/sessions/new" element={<NewSessionPage />} />
              <Route path="/sessions/history" element={<div>Session History (To be implemented)</div>} />
              <Route path="/evaluation/new" element={<PostTreatmentEvaluation />} />
            </Route>
          </Route>

          {/* Catch-all route */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
