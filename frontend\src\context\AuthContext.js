import React, { createContext, useContext, useState, useEffect } from 'react';
import { localStorageService } from '../services/localStorageService';

/**
 * Authentication context for managing user state throughout the application.
 * Provides centralized authentication management including login, logout, and role-based access control.
 * Used by all protected routes and components that need authentication state.
 */
const AuthContext = createContext();

/**
 * Custom hook to access authentication context values.
 * Provides convenient access to authentication state and methods from any component.
 * 
 * @returns {Object} Authentication context containing:
 *   - currentUser: Current authenticated user object
 *   - userRole: User's assigned role (doctor, nurse, admin)
 *   - isLoading: Loading state for authentication operations
 *   - login: Function to authenticate user
 *   - logout: Function to sign out user
 *   - setRole: Function to set user role
 *   - medplum: Mock object for compatibility
 * @throws {Error} If used outside of AuthProvider
 */
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

/**
 * Authentication provider component that wraps the application.
 * Manages authentication state and provides auth methods to all child components.
 * Handles user session persistence and restoration on app reload.

 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components that need access to auth context
 */
export function AuthProvider({ children }) {
  const [currentUser, setCurrentUser] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  /**
   * Authenticates user with email and password credentials.
   * Currently uses mock authentication for demo purposes.
 
   * @param {string} email - User's email address
   * @param {string} password - User's password
   * @returns {Promise<boolean>} Success status of the authentication attempt
   */
  const login = async (email, password) => {
    try {
      setIsLoading(true);

      // Input validation
      if (!email || !password) {
        throw new Error('Email and password are required');
      }

      // For demo purposes, we'll use a mock login
      const mockUser = {
        id: `user_${Date.now()}`,
        name: email.split('@')[0],
        email: email.toLowerCase(),
        lastLogin: new Date().toISOString(),
      };

      setCurrentUser(mockUser);
      localStorageService.setCurrentUser(mockUser);

      return true;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Logs out the current user and clears all authentication data.
   * Removes user data from both application state and localStorage.
   * Redirects user to login page after successful logout.
   */
  const logout = async () => {
    try {
      setIsLoading(true);

      // Clear application state
      setCurrentUser(null);
      setUserRole(null);

      // Clear localStorage
      localStorageService.setCurrentUser(null);
      localStorageService.setUserRole(null);

    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Sets the user's role and persists it to localStorage.
   * Used after login to define user's access permissions throughout the application.
   * Determines which dashboard and features the user can access.
   * 
   * @param {string} role - User role: 'doctor', 'nurse', or 'admin'
   */
  const setRole = (role) => {
    const validRoles = ['doctor', 'nurse', 'admin'];

    if (!validRoles.includes(role)) {
      console.error(`Invalid role: ${role}. Must be one of: ${validRoles.join(', ')}`);
      return;
    }

    setUserRole(role);
    localStorageService.setUserRole(role);
  };

  const setUser = (user) => {
    setCurrentUser(user);
    localStorageService.setCurrentUser(user);
  };

  /**
   * Checks for existing authentication data on application startup.
   * Restores user session if valid authentication data exists in localStorage.
   * Handles initial loading state for the application.
   */
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const storedUser = localStorageService.getCurrentUser();
        const storedRole = localStorageService.getUserRole();

        if (storedUser) {
          // Validate stored user data
          if (storedUser.email && storedUser.id) {
            setCurrentUser(storedUser);

            if (storedRole) {
              setUserRole(storedRole);
            }
          } else {
            // Invalid stored data, clear it
            localStorageService.setCurrentUser(null);
            localStorageService.setUserRole(null);
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        // Clear potentially corrupted data
        localStorageService.setCurrentUser(null);
        localStorageService.setUserRole(null);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Context value object containing all authentication state and methods
  const authValue = {
    currentUser,
    userRole,
    isLoading,
    login,
    logout,
    setRole,
    setUser,
    // Mock medplum object for compatibility with existing components
    // This maintains compatibility with components that expect medplum integration
    medplum: {
      isSignedIn: () => currentUser !== null
    },
  };

  return (
    <AuthContext.Provider value={authValue}>
      {!isLoading && children}
    </AuthContext.Provider>
  );
}