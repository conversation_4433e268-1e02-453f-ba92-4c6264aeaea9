/**
 * @file TabComponents.jsx
 * Component for rendering tab navigation and content in post-treatment evaluation.
 */

import VitalsTab from '../tabs/VitalsTab';
import FunctionalTestsTab from '../tabs/FunctionalTestsTab';
import QualityOfLifeTab from '../tabs/QualityOfLifeTab';
import SymptomsTab from '../tabs/SymptomsTab';
import BiomarkersTab from '../tabs/BiomarkersTab';
import { EVALUATION_TABS } from '../constants/postTreatmentConstants';

/**
 * Renders the tab navigation buttons for different evaluation sections.
 * 
 * @param {Object} props - Component props
 * @param {string} props.activeTab - The currently active tab
 * @param {Function} props.setActiveTab - Function to change the active tab
 * @returns {JSX.Element} Tab navigation component
 */
export const TabNavigation = ({ activeTab, setActiveTab }) => (
    <div className="bg-white border-b border-gray-200">
        <nav className="flex px-6">
            <button
                onClick={() => setActiveTab(EVALUATION_TABS.VITALS)}
                className={`px-4 py-3 font-medium text-sm border-b-2 ${activeTab === EVALUATION_TABS.VITALS
                    ? 'border-blue-600 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
            >
                Vitals
            </button>
            <button
                onClick={() => setActiveTab(EVALUATION_TABS.FUNCTIONAL_TESTS)}
                className={`ml-8 px-4 py-3 font-medium text-sm border-b-2 ${activeTab === EVALUATION_TABS.FUNCTIONAL_TESTS
                    ? 'border-blue-600 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
            >
                Functional Tests
            </button>
            <button
                onClick={() => setActiveTab(EVALUATION_TABS.QUALITY_OF_LIFE)}
                className={`ml-8 px-4 py-3 font-medium text-sm border-b-2 ${activeTab === EVALUATION_TABS.QUALITY_OF_LIFE
                    ? 'border-blue-600 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
            >
                Quality of Life
            </button>
            <button
                onClick={() => setActiveTab(EVALUATION_TABS.SYMPTOMS)}
                className={`ml-8 px-4 py-3 font-medium text-sm border-b-2 ${activeTab === EVALUATION_TABS.SYMPTOMS
                    ? 'border-blue-600 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
            >
                Symptoms
            </button>
            <button
                onClick={() => setActiveTab(EVALUATION_TABS.BIOMARKERS)}
                className={`ml-8 px-4 py-3 font-medium text-sm border-b-2 ${activeTab === EVALUATION_TABS.BIOMARKERS
                    ? 'border-blue-600 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
            >
                Biomarkers
            </button>
        </nav>
    </div>
);

/**
 * Renders the appropriate tab content based on the active tab selection.
 * 
 * @param {Object} props - Component props
 * @param {string} props.activeTab - The currently active tab
 * @param {Object} props.evaluation - The evaluation data object
 * @param {Function} props.handleInputChange - The input change handler function
 * @returns {JSX.Element} Active tab content component
 */
export const TabContent = ({ activeTab, evaluation, handleInputChange }) => {
    switch (activeTab) {
        case EVALUATION_TABS.VITALS:
            return (
                <VitalsTab
                    vitals={evaluation.vitals}
                    onChange={(field, value) => handleInputChange('vitals', field, value)}
                />
            );
        case EVALUATION_TABS.FUNCTIONAL_TESTS:
            return (
                <FunctionalTestsTab
                    functionalTests={evaluation.functionalTests}
                    onChange={(field, value) => handleInputChange('functionalTests', field, value)}
                />
            );
        case EVALUATION_TABS.QUALITY_OF_LIFE:
            return (
                <QualityOfLifeTab
                    qualityOfLife={evaluation.qualityOfLife}
                    onChange={(field, value) => handleInputChange('qualityOfLife', field, value)}
                />
            );
        case EVALUATION_TABS.SYMPTOMS:
            return (
                <SymptomsTab
                    symptoms={evaluation.symptoms}
                    onChange={(field, value) => handleInputChange('symptoms', field, value)}
                />
            );
        case EVALUATION_TABS.BIOMARKERS:
            return (
                <BiomarkersTab
                    biomarkers={evaluation.biomarkers}
                    onChange={(field, value) => handleInputChange('biomarkers', field, value)}
                />
            );
        default:
            return null;
    }
};