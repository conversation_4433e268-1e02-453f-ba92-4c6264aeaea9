/**
 * @file evaluationUtils.js
 * Utility functions for evaluation progress calculations and data operations
 */

import { PROGRESS_STATUS } from '../constants/postTreatmentConstants';

/**
 * Calculates the completion status for a section based on required fields
 * 
 * @param {Object} sectionData - The data for a specific section
 * @param {Array} requiredFields - Array of required field names
 * @returns {string} Progress status (completed, in_progress, pending)
 */
export const calculateSectionProgress = (sectionData, requiredFields) => {
    if (!sectionData || !requiredFields || requiredFields.length === 0) {
        return PROGRESS_STATUS.PENDING;
    }

    // Count how many required fields are filled
    const filledFields = requiredFields.filter(field => {
        const value = sectionData[field];
        return value && value.toString().trim() !== '';
    });

    // Determine status based on completion
    if (filledFields.length === 0) {
        return PROGRESS_STATUS.PENDING;
    } else if (filledFields.length === requiredFields.length) {
        return PROGRESS_STATUS.COMPLETED;
    } else {
        return PROGRESS_STATUS.IN_PROGRESS;
    }
};

/**
 * Required fields for each evaluation section
 */
const REQUIRED_FIELDS = {
    vitals: ['height', 'weight', 'systolic', 'diastolic', 'heartRate', 'spo2'],
    functionalTests: ['nyhaClass', 'ccsClass', 'lvef', 'walkDistance'],
    qualityOfLife: ['energyLevel', 'sleepQuality', 'physicalActivity'],
    symptoms: ['anginaFrequency', 'anginaSeverity', 'shortnessOfBreath'],
    biomarkers: ['bnp', 'troponin', 'creatinine']
};

/**
 * Calculates the overall progress percentage based on actual form completion
 * 
 * @param {Object} evaluation - Complete evaluation object
 * @returns {Object} Progress information including percentage and section statuses
 */
export const calculateActualProgress = (evaluation) => {
    const sectionProgresses = {};
    const progressValues = {
        [PROGRESS_STATUS.COMPLETED]: 1,
        [PROGRESS_STATUS.IN_PROGRESS]: 0.5,
        [PROGRESS_STATUS.PENDING]: 0
    };

    // Calculate progress for each section based on actual data
    Object.keys(REQUIRED_FIELDS).forEach(sectionKey => {
        const sectionData = evaluation[sectionKey];
        const requiredFields = REQUIRED_FIELDS[sectionKey];
        sectionProgresses[sectionKey] = calculateSectionProgress(sectionData, requiredFields);
    });

    // Calculate overall percentage
    const sections = Object.values(sectionProgresses);
    const totalProgress = sections.reduce((acc, status) => acc + progressValues[status], 0);
    const percentage = Math.round((totalProgress / sections.length) * 100);

    return {
        percentage,
        sectionProgresses,
        completedSections: sections.filter(status => status === PROGRESS_STATUS.COMPLETED).length,
        totalSections: sections.length
    };
};

/**
 * Updates evaluation progress based on current form data
 * 
 * @param {Object} evaluation - Current evaluation state
 * @returns {Object} Updated evaluation with correct progress statuses
 */
export const updateEvaluationProgress = (evaluation) => {
    const { sectionProgresses } = calculateActualProgress(evaluation);

    return {
        ...evaluation,
        progress: sectionProgresses
    };
};

/**
 * Calculates the overall progress percentage based on section completion status.
 * Uses weighted values for different progress states.
 * 
 * @param {Object} evaluationProgress - Progress object from evaluation state
 * @returns {number} Progress percentage (0-100)
 */
export const calculateProgress = (evaluationProgress) => {
    const progressValues = {
        [PROGRESS_STATUS.COMPLETED]: 1,
        [PROGRESS_STATUS.IN_PROGRESS]: 0.5,
        [PROGRESS_STATUS.PENDING]: 0
    };

    const sections = Object.values(evaluationProgress);
    const totalProgress = sections.reduce((accumulator, status) =>
        accumulator + progressValues[status], 0
    );

    return Math.round((totalProgress / sections.length) * 100);
};

/**
 * Populates evaluation form with existing patient outcome measures
 * 
 * @param {Object} currentEvaluation - Current evaluation state
 * @param {Object} patientData - Patient object containing outcome measures
 * @returns {Object} Updated evaluation object with populated data
 */
export const populateEvaluationFromPatient = (currentEvaluation, patientData) => {
    if (!patientData || !patientData.outcomeMeasures?.current) {
        return currentEvaluation;
    }

    const { outcomeMeasures } = patientData;

    return {
        ...currentEvaluation,
        vitals: {
            ...currentEvaluation.vitals,
            systolic: outcomeMeasures.current.bloodPressure?.systolic || '',
            diastolic: outcomeMeasures.current.bloodPressure?.diastolic || '',
        },
        functionalTests: {
            ...currentEvaluation.functionalTests,
            ccsClass: outcomeMeasures.current.anginaClass || '',
            lvef: outcomeMeasures.current.lvef || '',
            walkDistance: outcomeMeasures.current.walkDistance || ''
        }
    };
};

/**
 * Creates outcome measures object from evaluation data
 * 
 * @param {Object} evaluation - Evaluation data object
 * @param {Object} existingOutcomeMeasures - Existing patient outcome measures
 * @returns {Object} Updated outcome measures object
 */
export const createOutcomeMeasuresFromEvaluation = (evaluation, existingOutcomeMeasures = {}) => {
    return {
        ...existingOutcomeMeasures,
        current: {
            date: new Date().toISOString().split('T')[0],
            bloodPressure: {
                systolic: evaluation.vitals.systolic,
                diastolic: evaluation.vitals.diastolic
            },
            anginaClass: evaluation.functionalTests.ccsClass,
            lvef: evaluation.functionalTests.lvef,
            walkDistance: evaluation.functionalTests.walkDistance,
            qualityOfLife: evaluation.qualityOfLife.energyLevel
        }
    };
};

/**
 * Gets completion status text for display
 * 
 * @param {string} status - Progress status constant
 * @returns {string} Human-readable status text
 */
export const getProgressStatusText = (status) => {
    return status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ');
};

/**
 * Gets section display name from key
 * 
 * @param {string} key - Section key (e.g., 'functionalTests')
 * @returns {string} Human-readable section name
 */
export const getSectionDisplayName = (key) => {
    return key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1');
};

/**
 * Validates if all required sections are completed
 * 
 * @param {Object} evaluationProgress - Progress object from evaluation state
 * @returns {boolean} True if all sections are completed
 */
export const isEvaluationComplete = (evaluationProgress) => {
    return Object.values(evaluationProgress).every(
        status => status === PROGRESS_STATUS.COMPLETED
    );
};

/**
 * Gets progress bar width for a given status
 * 
 * @param {string} status - Progress status constant
 * @returns {number} Width percentage (0-100)
 */
export const getProgressBarWidth = (status) => {
    switch (status) {
        case PROGRESS_STATUS.COMPLETED:
            return 100;
        case PROGRESS_STATUS.IN_PROGRESS:
            return 50;
        case PROGRESS_STATUS.PENDING:
        default:
            return 0;
    }
};