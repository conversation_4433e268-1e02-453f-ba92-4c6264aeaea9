/**
 * @file ActiveSession.jsx
 * Component for displaying active session monitoring view.
 */


/**
 * Renders the active session monitoring view with timer and controls.
 *
 * @param {Object} props
 * @param {number} props.elapsedTime - Current elapsed time in seconds
 * @param {Function} props.formatTime - Function to format time display
 * @param {Function} props.onStopSession - Callback to stop the session
 */
const ActiveSession = ({ elapsedTime, formatTime, onStopSession }) => {
    return (
        <div className="max-w-lg mx-auto text-center">
            <div className="mb-8">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <div className="w-8 h-8 bg-green-500 rounded-full animate-pulse"></div>
                </div>
                <h1 className="text-2xl font-light text-gray-900 mb-2">Session active</h1>
                <p className="text-gray-500">EECP treatment in progress</p>
            </div>

            <div className="bg-gray-50 rounded-2xl p-8 mb-8">
                <div className="text-5xl font-light text-gray-900 mb-2 font-mono">
                    {formatTime(elapsedTime)}
                </div>
                <div className="text-gray-500">Duration</div>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-8 text-sm">
                <div className="bg-gray-50 rounded-lg p-4">
                    <div className="font-medium text-gray-900">15:00</div>
                    <div className="text-gray-500">First checkpoint</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                    <div className="font-medium text-gray-900">45:00</div>
                    <div className="text-gray-500">Second checkpoint</div>
                </div>
            </div>

            <button
                onClick={onStopSession}
                className="w-full bg-red-500 hover:bg-red-600 text-white font-medium py-4 px-6 rounded-lg transition-colors"
            >
                Stop session
            </button>
        </div>
    );
};

export default ActiveSession;