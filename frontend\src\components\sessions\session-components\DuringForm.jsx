/**
 * @file DuringForm.jsx
 * Component for checkpoint data collection during session.
 */


/**
 * Renders the during-session checkpoint form for collecting measurements.
 *
 * @param {Object} props
 * @param {string} props.stage - Stage identifier ('15' or '45')
 * @param {Object} props.sessionData - Current session data
 * @param {Function} props.onInputChange - Callback for input changes
 * @param {Function} props.onContinue - Callback to continue session
 */
const DuringForm = ({ stage, sessionData, onInputChange, onContinue }) => {
    const section = stage === '15' ? 'during15Min' : 'during45Min';
    const timeLabel = stage === '15' ? '15-minute' : '45-minute';

    return (
        <div className="max-w-2xl mx-auto">
            <div className="text-center mb-12">
                <h1 className="text-3xl font-light text-gray-900 mb-3">{timeLabel} checkpoint</h1>
                <p className="text-gray-500">Record measurements during treatment</p>
            </div>

            <div className="space-y-6 mb-12">
                <div className="flex space-x-4">
                    <div className="flex-1">
                        <label className="block text-sm font-medium text-gray-700 mb-2">DS Peak</label>
                        <input
                            type="text"
                            inputMode="decimal"
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                            value={sessionData.measurements[section].dsPeak}
                            onChange={(e) => {
                                const value = e.target.value;
                                if (value === '' || /^\d*\.?\d*$/.test(value)) {
                                    onInputChange(section, 'dsPeak', value);
                                }
                            }}
                            placeholder="1.50"
                        />
                    </div>
                    <div className="flex-1">
                        <label className="block text-sm font-medium text-gray-700 mb-2">DS Area</label>
                        <input
                            type="text"
                            inputMode="decimal"
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                            value={sessionData.measurements[section].dsArea}
                            onChange={(e) => {
                                const value = e.target.value;
                                if (value === '' || /^\d*\.?\d*$/.test(value)) {
                                    onInputChange(section, 'dsArea', value);
                                }
                            }}
                            placeholder="0.85"
                        />
                    </div>
                </div>

                <div className="flex space-x-4">
                    <div className="flex-1">
                        <label className="block text-sm font-medium text-gray-700 mb-2">Pulse rate</label>
                        <input
                            type="text"
                            inputMode="numeric"
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                            value={sessionData.measurements[section].pulseRate}
                            onChange={(e) => {
                                const value = e.target.value;
                                if (value === '' || /^\d+$/.test(value)) {
                                    onInputChange(section, 'pulseRate', value);
                                }
                            }}
                            placeholder="65"
                        />
                        <div className="text-xs text-gray-500 mt-1">BPM</div>
                    </div>
                    <div className="flex-1">
                        <label className="block text-sm font-medium text-gray-700 mb-2">Applied pressure</label>
                        <input
                            type="text"
                            inputMode="numeric"
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                            value={sessionData.measurements[section].appliedPressure}
                            onChange={(e) => {
                                const value = e.target.value;
                                if (value === '' || /^\d+$/.test(value)) {
                                    onInputChange(section, 'appliedPressure', value);
                                }
                            }}
                            placeholder="245"
                        />
                        <div className="text-xs text-gray-500 mt-1">mmHg</div>
                    </div>
                </div>
            </div>

            <button
                onClick={onContinue}
                className="w-full bg-black hover:bg-gray-800 text-white font-medium py-4 px-6 rounded-lg transition-colors"
            >
                Continue session
            </button>
        </div>
    );
};

export default DuringForm;