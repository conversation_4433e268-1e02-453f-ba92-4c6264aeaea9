"""
Main FastAPI application with Supabase integration
"""

import os
from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import uvicorn
from dotenv import load_dotenv

from helpers.supabase_setup import supabase_client, get_supabase

# Load environment variables
load_dotenv()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    print("🚀 Starting SyncoreV1 API...")

    # Test Supabase connection
    try:
        connection_status = await supabase_client.test_connection()
        print(f"📊 Supabase connection status: {connection_status}")
    except Exception as e:
        print(f"❌ Supabase connection failed: {e}")

    yield

    # Shutdown
    print("🛑 Shutting down SyncoreV1 API...")
    await supabase_client.close_db_pool()

# Create FastAPI app
app = FastAPI(
    title="SyncoreV1 API",
    description="Backend API for SyncoreV1 EECP Management System",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("ALLOWED_ORIGINS", "http://localhost:3000").split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check endpoint
@app.get("/")
async def root():
    """Root endpoint - API health check"""
    return {
        "message": "SyncoreV1 API is running",
        "version": "1.0.0",
        "status": "healthy"
    }

@app.get("/health")
async def health_check():
    """Detailed health check including Supabase connection"""
    try:
        connection_status = await supabase_client.test_connection()
        return {
            "status": "healthy",
            "api": "running",
            "supabase": connection_status,
            "timestamp": "2024-01-15T10:30:00Z"
        }
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Service unavailable: {str(e)}")

# Supabase test endpoints
@app.get("/test/supabase")
async def test_supabase():
    """Test Supabase connection and basic operations"""
    try:
        supabase = get_supabase()

        # Test basic query (this will fail if no tables exist, but that's expected)
        try:
            response = supabase.table('patients').select("*").limit(1).execute()
            patients_test = "success"
        except Exception:
            patients_test = "no patients table (expected for new project)"

        return {
            "supabase_client": "initialized",
            "patients_table_test": patients_test,
            "connection": "successful"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Supabase test failed: {str(e)}")

# Example patient endpoints (you can expand these)
@app.get("/patients")
async def get_patients():
    """Get all patients from Supabase"""
    try:
        supabase = get_supabase()
        response = supabase.table('patients').select("*").execute()
        return {
            "success": True,
            "data": response.data,
            "count": len(response.data)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch patients: {str(e)}")

@app.post("/patients")
async def create_patient(patient_data: dict):
    """Create a new patient in Supabase"""
    try:
        supabase = get_supabase()
        response = supabase.table('patients').insert(patient_data).execute()
        return {
            "success": True,
            "message": "Patient created successfully",
            "data": response.data[0] if response.data else None
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create patient: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=os.getenv("API_HOST", "0.0.0.0"),
        port=int(os.getenv("API_PORT", 8000)),
        reload=os.getenv("DEBUG", "False").lower() == "true"
    )