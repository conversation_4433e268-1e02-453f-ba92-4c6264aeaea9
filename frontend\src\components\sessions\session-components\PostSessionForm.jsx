/**
 * @file PostSessionForm.jsx
 * Component for post-session assessment and final measurements.
 */

import { SKIN_CONDITIONS } from '../constants/sessionConstants';

/**
 * Renders the post-session form for final measurements and notes.
 *
 * @param {Object} props
 * @param {Object} props.sessionData - Current session data
 * @param {string} props.error - Error message to display
 * @param {Function} props.onInputChange - Callback for input changes
 * @param {Function} props.onNestedInputChange - Callback for nested input changes
 * @param {Function} props.onNotesChange - Callback for notes changes
 * @param {Function} props.onCompleteSession - Callback to complete session
 */
const PostSessionForm = ({
    sessionData,
    error,
    onInputChange,
    onNestedInputChange,
    onNotesChange,
    onCompleteSession
}) => {
    return (
        <div className="max-w-2xl mx-auto">
            <div className="text-center mb-12">
                <h1 className="text-3xl font-light text-gray-900 mb-3">Post-session assessment</h1>
                <p className="text-gray-500">Complete final measurements and notes</p>
            </div>

            {/* Show error if exists */}
            {error && (
                <div className="mb-6 bg-red-50 border-l-4 border-red-400 p-3 text-red-700">
                    {error}
                </div>
            )}

            <div className="space-y-8 mb-12">
                {/* Blood Pressure */}
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Blood pressure</label>
                    <div className="flex space-x-4">
                        <div className="flex-1">
                            <input
                                type="text"
                                inputMode="numeric"
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                                value={sessionData.measurements.postEECP.bloodPressure.systolic}
                                onChange={(e) => {
                                    const value = e.target.value;
                                    if (value === '' || /^\d+$/.test(value)) {
                                        onNestedInputChange('postEECP', 'bloodPressure', 'systolic', value);
                                    }
                                }}
                                placeholder="115"
                            />
                            <div className="text-xs text-gray-500 mt-1">Systolic</div>
                        </div>
                        <div className="flex items-center text-gray-400 pb-6">/</div>
                        <div className="flex-1">
                            <input
                                type="text"
                                inputMode="numeric"
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                                value={sessionData.measurements.postEECP.bloodPressure.diastolic}
                                onChange={(e) => {
                                    const value = e.target.value;
                                    if (value === '' || /^\d+$/.test(value)) {
                                        onNestedInputChange('postEECP', 'bloodPressure', 'diastolic', value);
                                    }
                                }}
                                placeholder="72"
                            />
                            <div className="text-xs text-gray-500 mt-1">Diastolic</div>
                        </div>
                    </div>
                </div>

                {/* Pulse and SpO2 */}
                <div className="flex space-x-4">
                    <div className="flex-1">
                        <label className="block text-sm font-medium text-gray-700 mb-2">Pulse rate</label>
                        <input
                            type="text"
                            inputMode="numeric"
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                            value={sessionData.measurements.postEECP.pulseRate}
                            onChange={(e) => {
                                const value = e.target.value;
                                if (value === '' || /^\d+$/.test(value)) {
                                    onInputChange('postEECP', 'pulseRate', value);
                                }
                            }}
                            placeholder="60"
                        />
                        <div className="text-xs text-gray-500 mt-1">BPM</div>
                    </div>
                    <div className="flex-1">
                        <label className="block text-sm font-medium text-gray-700 mb-2">SpO2</label>
                        <input
                            type="text"
                            inputMode="numeric"
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                            value={sessionData.measurements.postEECP.spo2}
                            onChange={(e) => {
                                const value = e.target.value;
                                if (value === '' || /^\d+$/.test(value)) {
                                    onInputChange('postEECP', 'spo2', value);
                                }
                            }}
                            placeholder="99"
                        />
                        <div className="text-xs text-gray-500 mt-1">%</div>
                    </div>
                </div>

                {/* Skin Condition */}
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Skin condition</label>
                    <select
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent bg-white"
                        value={sessionData.measurements.postEECP.skinCondition}
                        onChange={(e) => onInputChange('postEECP', 'skinCondition', e.target.value)}
                    >
                        {SKIN_CONDITIONS.map(condition => (
                            <option key={condition} value={condition}>
                                {condition}
                            </option>
                        ))}
                    </select>
                </div>

                {/* Electrode Artifact */}
                <div className="flex items-center space-x-3">
                    <input
                        type="checkbox"
                        id="electrodeArtefact"
                        checked={sessionData.measurements.postEECP.electrodeInducedArtefact}
                        onChange={(e) => onInputChange('postEECP', 'electrodeInducedArtefact', e.target.checked)}
                        className="w-4 h-4 text-black focus:ring-black border-gray-300 rounded"
                    />
                    <label htmlFor="electrodeArtefact" className="text-sm font-medium text-gray-700">
                        Electrode induced artifact present
                    </label>
                </div>

                {/* Notes */}
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Session notes</label>
                    <textarea
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent resize-none"
                        rows="4"
                        value={sessionData.notes}
                        onChange={(e) => onNotesChange(e.target.value)}
                        placeholder="Enter any observations, patient feedback, or important notes..."
                    />
                </div>
            </div>

            <button
                onClick={onCompleteSession}
                className="w-full bg-black hover:bg-gray-800 text-white font-medium py-4 px-6 rounded-lg transition-colors"
            >
                Complete session
            </button>
        </div>
    );
};

export default PostSessionForm;