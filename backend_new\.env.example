# Supabase Configuration
# Get these values from your Supabase project dashboard

# Your Supabase project URL (found in Settings > API)
SUPABASE_URL=https://your-project-ref.supabase.co

# Your Supabase anon/public key (found in Settings > API)
SUPABASE_ANON_KEY=your-anon-key-here

# Your Supabase service role key (found in Settings > API) - KEEP SECRET!
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Your database password (found in Settings > Database)
SUPABASE_DB_PASSWORD=your-database-password-here

# Application Configuration
APP_NAME=SyncoreV1
APP_VERSION=1.0.0
DEBUG=True

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000

# CORS Configuration (for frontend integration)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,http://127.0.0.1:3000

# JWT Configuration (if using custom auth)
JWT_SECRET_KEY=your-jwt-secret-key-here
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30
