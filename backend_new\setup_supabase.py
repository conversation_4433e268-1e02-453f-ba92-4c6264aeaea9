#!/usr/bin/env python3
"""
Supabase Setup Script for SyncoreV1

This script helps you set up your Supabase connection and create initial database tables.
Run this after setting up your .env file with Supabase credentials.
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# Add the app directory to the path so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from helpers.supabase_setup import supabase_client, get_supabase

async def test_connection():
    """Test the Supabase connection"""
    print("🔍 Testing Supabase connection...")
    
    try:
        status = await supabase_client.test_connection()
        print(f"✅ Connection test results:")
        print(f"   - Supabase API: {status['supabase_api']}")
        print(f"   - Database: {status['database']}")
        print(f"   - Project URL: {status['url']}")
        print(f"   - Project Ref: {status['project_ref']}")
        return True
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False

async def create_patients_table():
    """Create the patients table if it doesn't exist"""
    print("📋 Creating patients table...")
    
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS patients (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        
        -- Basic Information
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        date_of_birth DATE,
        gender VARCHAR(20),
        
        -- Contact Information
        email VARCHAR(255),
        phone VARCHAR(50),
        address JSONB,
        
        -- Medical Information
        medical_record_number VARCHAR(100) UNIQUE,
        primary_diagnosis TEXT,
        medical_history JSONB,
        medications JSONB,
        allergies JSONB,
        
        -- EECP Specific
        intake_reason TEXT,
        eecp_status VARCHAR(50) DEFAULT 'pending',
        session_count INTEGER DEFAULT 0,
        
        -- Vitals and Measurements
        vitals JSONB,
        cardiac_measurements JSONB,
        
        -- Metadata
        active BOOLEAN DEFAULT TRUE,
        notes TEXT
    );
    """
    
    # Create updated_at trigger
    trigger_sql = """
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
    END;
    $$ language 'plpgsql';
    
    DROP TRIGGER IF EXISTS update_patients_updated_at ON patients;
    CREATE TRIGGER update_patients_updated_at
        BEFORE UPDATE ON patients
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    """
    
    try:
        from helpers.supabase_setup import execute_query
        
        # Create table
        await execute_query(create_table_sql)
        print("✅ Patients table created successfully")
        
        # Create trigger
        await execute_query(trigger_sql)
        print("✅ Updated_at trigger created successfully")
        
        return True
    except Exception as e:
        print(f"❌ Failed to create patients table: {e}")
        return False

async def create_sessions_table():
    """Create the EECP sessions table"""
    print("📋 Creating sessions table...")
    
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS eecp_sessions (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        
        -- Patient Reference
        patient_id UUID REFERENCES patients(id) ON DELETE CASCADE,
        
        -- Session Information
        session_number INTEGER NOT NULL,
        session_date DATE NOT NULL,
        duration_minutes INTEGER,
        
        -- Pre-session Measurements
        pre_session_vitals JSONB,
        pre_session_symptoms JSONB,
        
        -- Session Parameters
        pressure_settings JSONB,
        treatment_notes TEXT,
        
        -- Post-session Measurements
        post_session_vitals JSONB,
        post_session_symptoms JSONB,
        
        -- Session Outcome
        session_status VARCHAR(50) DEFAULT 'completed',
        adverse_events TEXT,
        patient_tolerance VARCHAR(50),
        
        -- Staff Information
        technician_id VARCHAR(100),
        supervising_physician_id VARCHAR(100),
        
        UNIQUE(patient_id, session_number)
    );
    """
    
    try:
        from helpers.supabase_setup import execute_query
        await execute_query(create_table_sql)
        print("✅ EECP sessions table created successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to create sessions table: {e}")
        return False

async def setup_row_level_security():
    """Set up Row Level Security (RLS) for tables"""
    print("🔒 Setting up Row Level Security...")
    
    rls_sql = """
    -- Enable RLS on patients table
    ALTER TABLE patients ENABLE ROW LEVEL SECURITY;
    
    -- Enable RLS on sessions table
    ALTER TABLE eecp_sessions ENABLE ROW LEVEL SECURITY;
    
    -- Create policy for authenticated users to read all patients
    DROP POLICY IF EXISTS "Users can view all patients" ON patients;
    CREATE POLICY "Users can view all patients" ON patients
        FOR SELECT USING (auth.role() = 'authenticated');
    
    -- Create policy for authenticated users to insert patients
    DROP POLICY IF EXISTS "Users can insert patients" ON patients;
    CREATE POLICY "Users can insert patients" ON patients
        FOR INSERT WITH CHECK (auth.role() = 'authenticated');
    
    -- Create policy for authenticated users to update patients
    DROP POLICY IF EXISTS "Users can update patients" ON patients;
    CREATE POLICY "Users can update patients" ON patients
        FOR UPDATE USING (auth.role() = 'authenticated');
    
    -- Similar policies for sessions
    DROP POLICY IF EXISTS "Users can view all sessions" ON eecp_sessions;
    CREATE POLICY "Users can view all sessions" ON eecp_sessions
        FOR SELECT USING (auth.role() = 'authenticated');
    
    DROP POLICY IF EXISTS "Users can insert sessions" ON eecp_sessions;
    CREATE POLICY "Users can insert sessions" ON eecp_sessions
        FOR INSERT WITH CHECK (auth.role() = 'authenticated');
    
    DROP POLICY IF EXISTS "Users can update sessions" ON eecp_sessions;
    CREATE POLICY "Users can update sessions" ON eecp_sessions
        FOR UPDATE USING (auth.role() = 'authenticated');
    """
    
    try:
        from helpers.supabase_setup import execute_query
        await execute_query(rls_sql)
        print("✅ Row Level Security configured successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to setup RLS: {e}")
        return False

async def insert_sample_data():
    """Insert some sample data for testing"""
    print("📝 Inserting sample data...")
    
    try:
        supabase = get_supabase()
        
        # Sample patient data
        sample_patient = {
            "first_name": "John",
            "last_name": "Doe",
            "date_of_birth": "1965-05-15",
            "gender": "male",
            "email": "<EMAIL>",
            "phone": "+1-************",
            "address": {
                "street": "123 Main St",
                "city": "Toronto",
                "province": "ON",
                "postal_code": "M5V 3A8"
            },
            "primary_diagnosis": "Coronary Artery Disease",
            "intake_reason": "EECP therapy for stable angina",
            "medical_history": {
                "conditions": ["Hypertension", "Hyperlipidemia"],
                "surgeries": ["Appendectomy 1995"]
            },
            "medications": ["Metoprolol 50mg", "Atorvastatin 20mg"],
            "allergies": ["Penicillin"],
            "vitals": {
                "height": 175,
                "weight": 80,
                "blood_pressure": "140/90"
            },
            "cardiac_measurements": {
                "lvef": 45,
                "bnp": 150
            }
        }
        
        response = supabase.table('patients').insert(sample_patient).execute()
        print(f"✅ Sample patient created with ID: {response.data[0]['id']}")
        return True
    except Exception as e:
        print(f"❌ Failed to insert sample data: {e}")
        return False

async def main():
    """Main setup function"""
    print("🚀 SyncoreV1 Supabase Setup")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        print("📝 Please copy .env.example to .env and fill in your Supabase credentials")
        return
    
    # Test connection
    if not await test_connection():
        print("❌ Setup failed due to connection issues")
        return
    
    print("\n" + "=" * 50)
    
    # Create tables
    success = True
    success &= await create_patients_table()
    success &= await create_sessions_table()
    success &= await setup_row_level_security()
    
    if success:
        print("\n🎉 Database setup completed successfully!")
        
        # Ask if user wants sample data
        response = input("\n❓ Would you like to insert sample data? (y/n): ")
        if response.lower() in ['y', 'yes']:
            await insert_sample_data()
    else:
        print("\n❌ Setup completed with errors")
    
    # Close connections
    await supabase_client.close_db_pool()
    print("\n✅ Setup complete!")

if __name__ == "__main__":
    asyncio.run(main())
