/**
 * Custom hook for managing audio recording functionality.
 * Handles microphone access, recording state, and audio processing.
 */

import { useState, useRef } from 'react';

const AUDIO_CONFIG = {
    RECORDING_INTERVAL: 1000,
    AUDIO_TYPE: 'audio/wav'
};

/**
 * Custom hook for audio recording functionality.
 * 
 * @returns {Object} Recording state and control functions
 */
export const useAudioRecording = () => {
    const [isRecording, setIsRecording] = useState(false);
    const [recordingTime, setRecordingTime] = useState(0);
    const [isProcessing, setIsProcessing] = useState(false);

    const mediaRecorderRef = useRef(null);
    const audioChunksRef = useRef([]);
    const timerRef = useRef(null);

    /**
     * Starts audio recording.
     * 
     * @param {Function} onRecordingComplete - Callback when recording completes
     */
    const startRecording = async (onRecordingComplete) => {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            audioChunksRef.current = [];

            const mediaRecorder = new MediaRecorder(stream);
            mediaRecorderRef.current = mediaRecorder;

            mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    audioChunksRef.current.push(event.data);
                }
            };

            mediaRecorder.onstop = async () => {
                const audioBlob = new Blob(audioChunksRef.current, { type: AUDIO_CONFIG.AUDIO_TYPE });
                if (onRecordingComplete) {
                    await onRecordingComplete(audioBlob);
                }
            };

            mediaRecorder.start(AUDIO_CONFIG.RECORDING_INTERVAL);
            setIsRecording(true);

            setRecordingTime(0);
            timerRef.current = setInterval(() => {
                setRecordingTime(prev => prev + 1);
            }, 1000);

        } catch (error) {
            console.error('Error starting recording:', error);
            throw new Error('Could not access microphone. Please check permissions.');
        }
    };

    /**
     * Stops audio recording.
     */
    const stopRecording = () => {
        if (mediaRecorderRef.current && isRecording) {
            mediaRecorderRef.current.stop();
            mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
            setIsRecording(false);
            clearInterval(timerRef.current);
        }
    };

    /**
     * Toggles recording state.
     * 
     * @param {Function} onRecordingComplete - Callback when recording completes
     */
    const toggleRecording = (onRecordingComplete) => {
        if (isRecording) {
            stopRecording();
        } else {
            startRecording(onRecordingComplete);
        }
    };

    return {
        isRecording,
        recordingTime,
        isProcessing,
        setIsProcessing,
        startRecording,
        stopRecording,
        toggleRecording
    };
};