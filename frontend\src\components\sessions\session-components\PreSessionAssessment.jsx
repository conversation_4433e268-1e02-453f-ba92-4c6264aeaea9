
/**
 * Component for pre-session vitals and symptoms recording.
 */

/**
 * Handles pre-session assessment form.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.vitals - Vitals data object
 * @param {Object} props.symptoms - Symptoms data object
 * @param {Function} props.onVitalChange - Vital signs change handler
 * @param {Function} props.onSymptomChange - Symptoms change handler
 * @returns {JSX.Element} Pre-session assessment form
 */
const PreSessionAssessment = ({ vitals, symptoms, onVitalChange, onSymptomChange }) => {
    /**
     * Handles vital signs input changes.
     */
    const handleVitalChange = (e) => {
        const { name, value } = e.target;
        onVitalChange(name, value);
    };

    /**
     * Handles symptom input changes.
     */
    const handleSymptomChange = (e) => {
        const { name, checked, value, type } = e.target;
        onSymptomChange(name, type === 'checkbox' ? checked : value, type);
    };

    return (
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6">
                <h2 className="text-lg font-medium text-gray-900">Pre-Session Assessment</h2>
                <p className="mt-1 text-sm text-gray-500">Record vitals and symptoms before starting</p>
            </div>
            <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
                <div className="space-y-6">
                    <div>
                        <h3 className="text-md font-medium text-gray-900 mb-2">Vitals</h3>
                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                            <div>
                                <label htmlFor="preHeartRate" className="block text-sm font-medium text-gray-700">Heart Rate (bpm)</label>
                                <input
                                    type="number"
                                    id="preHeartRate"
                                    name="preHeartRate"
                                    value={vitals.preHeartRate}
                                    onChange={handleVitalChange}
                                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                                />
                            </div>
                            <div>
                                <label htmlFor="preBloodPressure" className="block text-sm font-medium text-gray-700">Blood Pressure (mmHg)</label>
                                <input
                                    type="text"
                                    id="preBloodPressure"
                                    name="preBloodPressure"
                                    placeholder="e.g. 120/80"
                                    value={vitals.preBloodPressure}
                                    onChange={handleVitalChange}
                                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                                />
                            </div>
                        </div>
                    </div>

                    <div>
                        <h3 className="text-md font-medium text-gray-900 mb-2">Symptoms</h3>
                        <div className="grid grid-cols-1 gap-2 sm:grid-cols-2">
                            <div className="flex items-center">
                                <input
                                    id="chestPain"
                                    name="chestPain"
                                    type="checkbox"
                                    checked={symptoms.chestPain}
                                    onChange={handleSymptomChange}
                                    className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                />
                                <label htmlFor="chestPain" className="ml-2 block text-sm text-gray-900">
                                    Chest Pain
                                </label>
                            </div>
                            <div className="flex items-center">
                                <input
                                    id="shortnessOfBreath"
                                    name="shortnessOfBreath"
                                    type="checkbox"
                                    checked={symptoms.shortnessOfBreath}
                                    onChange={handleSymptomChange}
                                    className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                />
                                <label htmlFor="shortnessOfBreath" className="ml-2 block text-sm text-gray-900">
                                    Shortness of Breath
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PreSessionAssessment;
