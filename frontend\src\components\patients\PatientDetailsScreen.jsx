import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { 
  ArrowLeft, 
  Calendar, 
  FileText, 
  Printer, 
  Info, 
  Check, 
  MoreHorizontal 
} from 'lucide-react';

import { 
  PATIENTS, 
  APPOINTMENTS,
  PRACTITIONERS,
  getAppointmentsByPatientId 
} from '../../data/mockData';
import MedicalHistoryTab from './MedicalHistoryTab';
import EvaluationDetailsModal from '../evaluations/EvaluationDetailsModal';
import { localStorageService } from '../../services/localStorageService';
import PatientNotes from './PatientNotes';
import EECPSessionsTimeline from './patient_tabs/EECPSessionsTab';
import MedicationTab from './patient_tabs/medicationTab';

// Add print styles
const printStyles = `
  @media print {
    @page {
      margin: 1.5cm;
      size: portrait;
    }
    
    body {
      -webkit-print-color-adjust: exact !important;
      print-color-adjust: exact !important;
      background: white !important;
    }

    /* Hide UI elements */
    .no-print, 
    .main-header,
    .nav-tabs,
    button,
    .modal {
      display: none !important;
    }

    /* Show all content */
    .tab-content {
      display: block !important;
    }

    /* Print header styling */
    .print-header {
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 2px solid #000;
    }

    /* Content styling */
    .content-wrapper {
      padding: 0 !important;
      margin: 0 !important;
    }

    /* Ensure all sections are visible */
    .overview-section,
    .medical-history-section,
    .sessions-section,
    .medications-section,
    .outcomes-section {
      display: block !important;
      page-break-inside: avoid;
      margin-bottom: 2rem;
    }

    /* Reset colors and backgrounds */
    .bg-gray-50, 
    .bg-white,
    .bg-blue-50,
    [class*="bg-"] {
      background-color: transparent !important;
    }

    /* Ensure text is visible */
    [class*="text-"] {
      color: black !important;
    }

    /* Preserve charts and graphs */
    .chart-container {
      break-inside: avoid;
      page-break-inside: avoid;
    }

    /* Ensure tables are readable */
    table {
      width: 100% !important;
      break-inside: avoid;
      page-break-inside: avoid;
    }

    /* Force show borders */
    .border,
    .border-t,
    .border-b,
    [class*="border-"] {
      border-color: #000 !important;
    }
  }
`;

function getPatientDisplayName(patient) {
  if (!patient) return '';
  if (typeof patient.name === 'string') return patient.name;
  if (Array.isArray(patient.name) && patient.name[0]) {
    const n = patient.name[0];
    return `${(n.given || []).join(' ')} ${n.family || ''}`.trim();
  }
  return 'Unknown';
}

const PatientDetailsScreen = () => {
  const { patientId } = useParams();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [patient, setPatient] = useState(null);
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [postTreatmentEvaluations, setPostTreatmentEvaluations] = useState([]);
  const contentRef = useRef(null);
  const [newAppointment, setNewAppointment] = useState({
    patientId: '',
    patientName: '',
    practitionerId: '',
    practitionerName: '',
    appointmentType: 'Follow-up Evaluation',
    date: new Date().toISOString().split('T')[0],
    startTime: '09:00',
    endTime: '09:30',
    location: 'Room 101',
    notes: ''
  });
  const [selectedEvaluation, setSelectedEvaluation] = useState(null);  
  const [structuredNote, setStructuredNote] = useState({
    summary: '',
    subjective: '',
    objective: '',
    assessment: '',
    plan: ''
  });

  useEffect(() => {
    const fetchPatientData = async () => {
      try {
        setLoading(true);

        // Fetch patient from backend API
        const res = await fetch(`http://localhost:4000/api/patients/${patientId}`);
        if (!res.ok) throw new Error('Patient not found');
        const foundPatient = await res.json();

        // Generate SOAP notes based on patient data
        // The following code is similar to that in SessionExecutionPanel.js, line 387
        // TODO: Merge them together.
        const getSoapNotes = async (patient) => {
          const apiKey = process.env.REACT_APP_OPENAI_API_KEY;   
          console.log('Starting API call to GPT-4 to generate SOAP notes...');
          try {
            const soapResponse = await fetch('https://api.openai.com/v1/chat/completions', {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                model: "gpt-4",
                messages: [
                  {
                    role: "system",
                    content: `You are a medical scribe assistant. Convert the following patient data, in JSON format, from an EECP therapy session into a structured SOAP note.
                              Include relevant patient data and session information.
                              Format your response as a JSON object with the following structure:
                              {
                                "summary": "A brief summary of the patient's condition and the session",
                                "subjective": "Patient's reported symptoms and feelings",
                                "objective": "Measurable medication information",
                                "assessment": "Clinical assessment of patient's condition and response to therapy",
                                "plan": "Treatment plan and recommendations"
                              }
                              
                              IMPORTANT: Your response should include only plain text for each field. Do not include any other JSON objects.`
                  },
                  {
                    role: "user",
                    content: JSON.stringify(patient)
                  }
                ],
                temperature: 0.3
              })
            });
            
            if (!soapResponse.ok) {
              console.error('GPT-4 API error:', soapResponse.status);
              const errorText = await soapResponse.text();
              console.error('Error details:', errorText);
              throw new Error(`API error in chat completion: ${soapResponse.status} ${errorText}`);
            }
            
            const soapData = await soapResponse.json();
            console.log('GPT-4 API response:', soapData);
            
            // Check if the response has the expected structure
            if (!soapData || !soapData.choices || !soapData.choices[0] || !soapData.choices[0].message || !soapData.choices[0].message.content) {
              console.error('Unexpected response format:', soapData);
              throw new Error('Unexpected response format from OpenAI API');
            }
            
            try {
              const contentStr = soapData.choices[0].message.content;
              console.log('Content to parse:', contentStr);
              const structuredSOAP = JSON.parse(contentStr);
              console.log('Parsed SOAP:', structuredSOAP);
              
              // Ensure the structuredSOAP has all required fields
              const defaultSOAP = {
                summary: '',
                subjective: '',
                objective: '',
                assessment: '',
                plan: ''
              };
              
              const completeSOAP = {
                ...defaultSOAP,
                ...structuredSOAP
              };
              
              patient.soapNotes = completeSOAP;
              setStructuredNote(completeSOAP);
            } catch (parseError) {
              console.error('Error parsing JSON from OpenAI response:', parseError);
              throw new Error('Failed to parse structured data from OpenAI response');
            }
          } catch (error) {
            console.error('Error calling OpenAI API:', error);

            // Fallback: set a default SOAP note so UI doesn't break
            const fallbackSOAP = {
              summary: 'SOAP note could not be generated due to API error.',
              subjective: '',
              objective: '',
              assessment: '',
              plan: ''
            };
            patient.soapNotes = fallbackSOAP;
            setStructuredNote(fallbackSOAP);
            alert(`Error processing SOAP note with OpenAI API: ${error.message}. Falling back to simulation.`);
          }
        };
        
        if (foundPatient) {
          setPatient(foundPatient);
          setNewAppointment(prev => ({
            ...prev,
            patientId: foundPatient.id,
            patientName: foundPatient.name
          }));

          // If you have a backend for evaluations, fetch here. Otherwise, keep localStorage for now.
          // const evaluations = await fetch(`/api/evaluations?patientId=${patientId}`).then(r => r.json());
          const evaluations = []; // Replace with backend call if available
          setPostTreatmentEvaluations(evaluations);

          // Generate SOAP notes if they don't exist
          if (!foundPatient.soapNotes) {
            await getSoapNotes(foundPatient);
            setStructuredNote(foundPatient.soapNotes);
            // Optionally, update backend with new SOAP notes here
          } else {
            setStructuredNote(foundPatient.soapNotes);
          }
        } else {
          console.error('Patient not found');
          navigate('/patients');
        }
        setLoading(false);
      } catch (error) {
        console.error('Error fetching patient data:', error);
        setLoading(false);
      }
    };

    fetchPatientData();
  }, [patientId, navigate]);
  

  const handleNewAppointmentChange = (e) => {
    const { name, value } = e.target;
    setNewAppointment({
      ...newAppointment,
      [name]: value
    });
  };
  
  const handlePractitionerSelect = (e) => {
    const practitionerId = e.target.value;
    const practitioner = PRACTITIONERS.find(p => p.id === practitionerId);
    
    setNewAppointment({
      ...newAppointment,
      practitionerId,
      practitionerName: practitioner ? practitioner.name : ''
    });
  };
  
  // Schedule a follow-up appointment.
  const handleScheduleFollowUp = async () => {
    try {
    
      // For the demo, we'll update both the patient's next appointment and the APPOINTMENTS array
      const newAppt = {
        id: `appt${Date.now()}`,
        patientId: newAppointment.patientId,
        patientName: newAppointment.patientName,
        practitionerId: newAppointment.practitionerId,
        practitionerName: newAppointment.practitionerName,
        appointmentType: newAppointment.appointmentType,
        status: 'booked',
        start: `${newAppointment.date}T${newAppointment.startTime}:00`,
        end: `${newAppointment.date}T${newAppointment.endTime}:00`,
        location: newAppointment.location,
        notes: newAppointment.notes
      };
      
      // Update the patient's next appointment
      const updatedPatient = {
        ...patient,
        nextAppointment: `${newAppointment.date}T${newAppointment.startTime}:00`
      };
      
      setPatient(updatedPatient);
      setShowScheduleModal(false);
      
      // Add the new appointment to the APPOINTMENTS array
      APPOINTMENTS.push(newAppt);
      
      // Show success message
      alert('Follow-up appointment scheduled successfully!');
    } catch (error) {
      console.error('Error scheduling follow-up:', error);
      alert('Failed to schedule follow-up appointment. Please try again.');
    }
  };
  
  const generatePDF = async () => {
    if (isGenerating) return;
    setIsGenerating(true);

    try {
      // Force show all content before generating PDF
      const content = contentRef.current;
      if (content) {
        // Add temporary class to show all content
        content.classList.add('printing');
      }

      const canvas = await html2canvas(content, {
        scale: 2,
        useCORS: true,
        logging: false,
        backgroundColor: '#ffffff',
        onclone: (clonedDoc) => {
          // Ensure all content is visible in the clone
          const clonedContent = clonedDoc.querySelector('.content-wrapper');
          if (clonedContent) {
            clonedContent.style.height = 'auto';
            clonedContent.style.overflow = 'visible';
          }
        }
      });

      const imgWidth = 210; // A4 width in mm
      const pageHeight = 297; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      
      const pdf = new jsPDF('p', 'mm', 'a4');
      pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, 0, imgWidth, imgHeight);

      // Add more pages if content exceeds one page
      let heightLeft = imgHeight - pageHeight;
      let position = -pageHeight;

      while (heightLeft >= 0) {
        position = position - imgHeight;
        pdf.addPage();
        pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      // Open PDF in new window and print
      const pdfBlob = pdf.output('blob');
      const pdfUrl = URL.createObjectURL(pdfBlob);
      const printWindow = window.open(pdfUrl);
      
      printWindow.onload = () => {
        printWindow.print();
        URL.revokeObjectURL(pdfUrl);
      };
    } catch (error) {
      console.error('Error generating PDF:', error);
    } finally {
      // Remove temporary printing class
      const content = contentRef.current;
      if (content) {
        content.classList.remove('printing');
      }
      setIsGenerating(false);
    }
  };

  if (loading || !patient) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Create a fetch for the EECP Sessions. 

  // End of functions. Html components below.
  return (
    <div className="flex flex-col min-h-screen bg-gray-100" ref={contentRef}>
      <style>{printStyles}</style>
      
      {/* Print Header - only visible in print */}
      <div className="hidden print:block print-header">
        <div className="flex justify-between items-baseline">
          <div>
            <h1 className="text-2xl font-bold">{getPatientDisplayName(patient)}</h1>
            <p className="text-gray-600 mt-1">
              {patient?.age || 'N/A'} yrs • {patient?.gender || 'N/A'} • ID: {patient?.id}
            </p>
          </div>
          <div className="text-right">
            <p className="text-gray-600">Generated: {new Date().toLocaleDateString()}</p>
          </div>
        </div>
      </div>

      {/* Main Header - hidden in print */}
      <header className="main-header bg-white shadow-sm z-10 no-print">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center py-4">
            <button 
              onClick={() => navigate(-1)}
              className="mr-4 p-1 rounded-full text-gray-400 hover:text-gray-500 hover:bg-gray-100"
            >
              <ArrowLeft className="h-6 w-6" />
            </button>
            
            <div className="flex items-center">
              <div className="h-12 w-12 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center font-bold text-lg mr-4">
                {getPatientDisplayName(patient).split(' ').map(n => n[0]).join('')}
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">{getPatientDisplayName(patient)}</h1>
                <div className="flex items-center text-base text-gray-600 mt-1">
                  <span className="mr-4 font-medium">{patient?.age || 'N/A'} yrs • {patient?.gender || 'N/A'}</span>
                  <span className="mr-4 font-medium">ID: {patient?.id}</span>
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                    {patient?.status || 'active'}
                  </span>
                </div>
              </div>
            </div>
            
            <div className="ml-auto flex space-x-2">
              <button 
                onClick={() => navigate(`/evaluation/new?patientId=${patient.id}`)}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700"
              >
                <FileText className="h-4 w-4 mr-2" />
                Post-Treatment Evaluation
              </button>
              <button 
                onClick={() => setShowScheduleModal(true)}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
              >
                <Calendar className="h-4 w-4 mr-2" />
                Schedule Follow-up
              </button>
              <button 
                onClick={generatePDF}
                disabled={isGenerating}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <Printer className="h-4 w-4 mr-2" />
                {isGenerating ? 'Generating...' : 'Print Report'}
              </button>
              <button className="p-2 rounded-full text-gray-400 hover:text-gray-500 hover:bg-gray-100">
                <MoreHorizontal className="h-5 w-5" />
              </button>
            </div>
          </div>
          
          {/* Navigation Tabs */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('overview')}
                className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'overview'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Overview
              </button>
              <button
                onClick={() => setActiveTab('medicalHistory')}
                className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'medicalHistory'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Medical History
              </button>
              <button
                onClick={() => setActiveTab('sessions')}
                className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'sessions'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Sessions & Parameters
              </button>
              <button
                onClick={() => setActiveTab('medications')}
                className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'medications'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Medications
              </button>
            </nav>
          </div>
        </div>
      </header>
      
      {/* Main content wrapper */}
      <div className="content-wrapper flex-1 py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Overview Section */}
          <div className="overview-section">
            <h2 className="text-xl font-bold mb-4 print:block hidden">Overview</h2>
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Treatment Summary Card */}
                <div className="bg-white shadow rounded-lg overflow-hidden">
                  <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                    <div className="flex justify-between items-center">
                      <h2 className="text-lg font-medium text-gray-900">Treatment Summary</h2>
                      <div className="text-sm text-gray-500">
                        <span className="inline-flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          {new Date(patient.lastVisit).toLocaleDateString()} - {new Date(patient.nextAppointment).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="px-6 py-5">
                    <div className="grid grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-3">EECP Treatment</h3>
                        <dl className="grid grid-cols-2 gap-x-4 gap-y-4">
                          <div>
                            <dt className="text-sm font-medium text-gray-500">Status</dt>
                            <dd className="mt-1 text-sm font-medium text-green-600">{patient.status}</dd>
                          </div>
                          <div>
                            <dt className="text-sm font-medium text-gray-500">Last Visit</dt>
                            <dd className="mt-1 text-sm text-gray-900">{new Date(patient.lastVisit).toLocaleDateString()}</dd>
                          </div>
                          <div>
                            <dt className="text-sm font-medium text-gray-500">Next Appointment</dt>
                            <dd className="mt-1 text-sm text-gray-900">{new Date(patient.nextAppointment).toLocaleDateString()}</dd>
                          </div>
                          <div>
                            <dt className="text-sm font-medium text-gray-500">Diagnosis</dt>
                            <dd className="mt-1 text-sm text-gray-900">{patient.diagnosis}</dd>
                          </div>
                        </dl>
                      </div>
                      
                      <div className="border-l border-gray-200 pl-6">
                        <h3 className="text-sm font-medium text-gray-500 mb-3">Clinical Parameters</h3>
                        <dl className="grid grid-cols-2 gap-x-4 gap-y-4">
                          <div>
                            <dt className="text-sm font-medium text-gray-500">LVEF</dt>
                            <dd className={`mt-1 text-sm font-medium ${patient.lvef < 30 ? 'text-red-600' : 'text-gray-900'}`}>
                              {patient.lvef}%
                            </dd>
                          </div>
                          <div>
                            <dt className="text-sm font-medium text-gray-500">BNP</dt>
                            <dd className={`mt-1 text-sm font-medium ${patient.bnp > 400 ? 'text-red-600' : 'text-gray-900'}`}>
                              {patient.bnp} pg/mL
                            </dd>
                          </div>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Key Outcome Measures Card */}
                <div className="bg-white shadow rounded-lg overflow-hidden">
                  <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                    <h2 className="text-lg font-medium text-gray-900">Key Outcome Measures</h2>
                    <div className="text-sm text-gray-500">
                      <span className="inline-flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        Baseline: {new Date(patient.outcomeMeasures?.baseline.date).toLocaleDateString()} - 
                        Current: {new Date(patient.outcomeMeasures?.current.date).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                  
                  <div className="px-6 py-5">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      {/* CCS Angina Class Card */}
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="flex items-center justify-between">
                          <h3 className="text-sm font-medium text-gray-500">CCS Angina Class</h3>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            -2 Classes
                          </span>
                        </div>
                        <div className="mt-2">
                          <div className="text-2xl font-bold text-blue-600">Class 1</div>
                          <div className="text-sm text-gray-500">from Class 3</div>
                        </div>
                        <div className="mt-2">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-blue-600 h-2 rounded-full" style={{ width: '75%' }}></div>
                          </div>
                        </div>
                      </div>

                      {/* Left Ventricular EF Card */}
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="flex items-center justify-between">
                          <h3 className="text-sm font-medium text-gray-500">Left Ventricular EF</h3>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            +6%
                          </span>
                        </div>
                        <div className="mt-2">
                          <div className="text-2xl font-bold text-blue-600">44%</div>
                          <div className="text-sm text-gray-500">from 38%</div>
                        </div>
                        <div className="mt-2">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-blue-600 h-2 rounded-full" style={{ width: '44%' }}></div>
                          </div>
                        </div>
                      </div>

                      {/* 6-Min Walk Distance Card */}
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="flex items-center justify-between">
                          <h3 className="text-sm font-medium text-gray-500">6-Min Walk Distance</h3>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            +105m
                          </span>
                        </div>
                        <div className="mt-2">
                          <div className="text-2xl font-bold text-blue-600">425m</div>
                          <div className="text-sm text-gray-500">from 320m</div>
                        </div>
                        <div className="mt-2">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-blue-600 h-2 rounded-full" style={{ width: '70%' }}></div>
                          </div>
                        </div>
                      </div>

                      {/* Angina Episodes Card */}
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="flex items-center justify-between">
                          <h3 className="text-sm font-medium text-gray-500">Angina Episodes</h3>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            -12/week
                          </span>
                        </div>
                        <div className="mt-2">
                          <div className="text-2xl font-bold text-blue-600">0</div>
                          <div className="text-sm text-gray-500">from 12 per week</div>
                        </div>
                        <div className="mt-2">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-blue-600 h-2 rounded-full" style={{ width: '100%' }}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Treatment Assessment Card */}
                <div className="bg-white shadow rounded-lg overflow-hidden">
                  <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                    <div className="flex justify-between items-center">
                      <h2 className="text-lg font-medium text-gray-900">Treatment Assessment</h2>
                      <div className="text-sm text-gray-500">
                        Generated {new Date().toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  
                  <div className="px-6 py-5 space-y-6">
                    {/* Executive Summary */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-start">
                        <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />
                        <div>
                          <h3 className="text-sm font-medium text-blue-800 mb-1">Executive Summary:</h3>
                          <p className="text-sm text-blue-700">
                            Patient has demonstrated an excellent response to EECP therapy with complete resolution of angina symptoms, improved functional capacity, and enhanced cardiac function. Treatment course was completed without complications, and significant improvements were observed across all clinical parameters.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {/* Clinical Improvements */}
                      <div>
                        <h3 className="text-sm font-medium text-gray-900 mb-4">Clinical Improvements</h3>
                        <ul className="space-y-3">
                          <li className="flex items-start">
                            <Check className="h-5 w-5 text-green-500 mt-0.5 mr-3" />
                            <span className="text-sm text-gray-700">CCS angina classification improved from Class III to Class I</span>
                          </li>
                          <li className="flex items-start">
                            <Check className="h-5 w-5 text-green-500 mt-0.5 mr-3" />
                            <span className="text-sm text-gray-700">LVEF increased from 38% to 44% (6% absolute improvement)</span>
                          </li>
                          <li className="flex items-start">
                            <Check className="h-5 w-5 text-green-500 mt-0.5 mr-3" />
                            <span className="text-sm text-gray-700">Six-minute walk distance increased from 320m to 425m (33% improvement)</span>
                          </li>
                          <li className="flex items-start">
                            <Check className="h-5 w-5 text-green-500 mt-0.5 mr-3" />
                            <span className="text-sm text-gray-700">Complete resolution of angina episodes (from 12/week to 0/week)</span>
                          </li>
                          <li className="flex items-start">
                            <Check className="h-5 w-5 text-green-500 mt-0.5 mr-3" />
                            <span className="text-sm text-gray-700">Blood pressure improved from 138/88 mmHg to 118/75 mmHg</span>
                          </li>
                        </ul>
                      </div>

                      {/* Recommendations */}
                      <div>
                        <h3 className="text-sm font-medium text-gray-900 mb-4">Recommendations</h3>
                        <ol className="space-y-3 list-decimal list-inside">
                          <li className="text-sm text-gray-700">Schedule 3-month follow-up (July 17, 2025)</li>
                          <li className="text-sm text-gray-700">Continue current medical management</li>
                          <li className="text-sm text-gray-700">Encourage moderate physical activity (30 min/day)</li>
                          <li className="text-sm text-gray-700">Monitor for recurrence of angina symptoms</li>
                          <li className="text-sm text-gray-700">Consider repeat EECP therapy if symptoms recur after 12 months</li>
                        </ol>
                        <div className="mt-4 flex justify-end">
                          <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <FileText className="h-4 w-4 mr-2" />
                            View Full Report
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* SOAP Notes Card */}
                <div className="bg-white shadow rounded-lg overflow-hidden">
                  <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                    <div className="flex justify-between items-center">
                      <h2 className="text-lg font-medium text-gray-900">SOAP Notes</h2>
                      <div className="text-sm text-gray-500">
                        Generated {new Date().toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  
                  <div className="px-6 py-5">{structuredNote.summary}</div>

                  <div className="px-6 py-5">
                    <div className="grid grid-cols-2 gap-6">
                      <div className="border-l border-gray-200 pl-6">
                        <h3 className="text-sm font-medium text-gray-900 mb-4">Subjective</h3>
                        <div className="bg-purple-50 p-3 rounded">{structuredNote.subjective}</div>
                      </div>
                      
                      <div className="border-l border-gray-200 pl-6">
                        <h3 className="text-sm font-medium text-gray-900 mb-4">Objective</h3>
                        <div className="bg-purple-50 p-3 rounded">{structuredNote.objective}</div>
                      </div>
                    </div>
                  </div>

                  <div className="px-6 py-5">
                    <div className="grid grid-cols-2 gap-6">
                      <div className="border-l border-gray-200 pl-6">
                        <h3 className="text-sm font-medium text-gray-900 mb-4">Assessment</h3>
                        <div className="bg-purple-50 p-3 rounded">{structuredNote.assessment}</div>
                      </div>
                      
                      <div className="border-l border-gray-200 pl-6">
                        <h3 className="text-sm font-medium text-gray-900 mb-4">Plan</h3>
                        <div className="bg-purple-50 p-3 rounded">{structuredNote.plan}</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Patient Notes */}
                {patient.notes && patient.notes.length > 0 && (
                  <div className="bg-white shadow rounded-lg overflow-hidden">
                    <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                      <h2 className="text-lg font-medium text-gray-900">Recent Notes</h2>
                    </div>
                    <div className="px-6 py-5">
                      <PatientNotes 
                        patient={patient} 
                        onNotesUpdate={setPatient}
                      />
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Medical History Section */}
          <div className="medical-history-section">
            <h2 className="text-xl font-bold mb-4 print:block hidden">Medical History</h2>
            {activeTab === 'medicalHistory' && (
              <MedicalHistoryTab patient={patient} />
            )}
          </div>

          {/* Sessions Section */}
          <div className="sessions-section">
            <h2 className="text-xl font-bold mb-4 print:block hidden">Sessions & Parameters</h2>
              {activeTab === 'sessions' && (
            <div className="space-y-6">

            {patient.sessions && patient.sessions.length > 0 ? (
              <EECPSessionsTimeline patientData={patient} />
            ) : (
              <div className="bg-white shadow rounded-lg p-6 text-center text-gray-500">
                No sessions available for this patient.
              </div>
            )}
            </div>
            )}
          </div>
          {/* Medications Section */}
          <div className="medications-section">
            <h2 className="text-xl font-bold mb-4 print:block hidden">Vitals & Trends</h2>
            {activeTab === 'medications' && (
              <MedicationTab patientData={patient} />
            )}
          </div>

        </div>
      </div>
      {/* Schedule Modal - hidden in print */}
      {showScheduleModal && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Schedule Follow-up for {patient.name}
                    </h3>
                    <div className="mt-4 space-y-4">
                      <div>
                        <label htmlFor="practitionerId" className="block text-sm font-medium text-gray-700">Provider</label>
                        <select
                          id="practitionerId"
                          name="practitionerId"
                          value={newAppointment.practitionerId}
                          onChange={handlePractitionerSelect}
                          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                          required
                        >
                          <option value="">Select Provider</option>
                          {PRACTITIONERS.map(practitioner => (
                            <option key={practitioner.id} value={practitioner.id}>{practitioner.name}</option>
                          ))}
                        </select>
                      </div>
                      
                      <div>
                        <label htmlFor="appointmentType" className="block text-sm font-medium text-gray-700">Appointment Type</label>
                        <select
                          id="appointmentType"
                          name="appointmentType"
                          value={newAppointment.appointmentType}
                          onChange={handleNewAppointmentChange}
                          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                          required
                        >
                          <option value="Follow-up Evaluation">Follow-up Evaluation</option>
                          <option value="EECP Session">EECP Session</option>
                          <option value="Consultation">Consultation</option>
                        </select>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label htmlFor="date" className="block text-sm font-medium text-gray-700">Date</label>
                          <input
                            type="date"
                            id="date"
                            name="date"
                            value={newAppointment.date}
                            onChange={handleNewAppointmentChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                            required
                          />
                        </div>
                        
                        <div>
                          <label htmlFor="location" className="block text-sm font-medium text-gray-700">Location</label>
                          <select
                            id="location"
                            name="location"
                            value={newAppointment.location}
                            onChange={handleNewAppointmentChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                            required
                          >
                            <option value="Room 101">Room 101</option>
                            <option value="Room 102">Room 102</option>
                            <option value="Room 103">Room 103</option>
                            <option value="Room 104">Room 104</option>
                            <option value="Room 105">Room 105</option>
                          </select>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label htmlFor="startTime" className="block text-sm font-medium text-gray-700">Start Time</label>
                          <input
                            type="time"
                            id="startTime"
                            name="startTime"
                            value={newAppointment.startTime}
                            onChange={handleNewAppointmentChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                            required
                          />
                        </div>
                        
                        <div>
                          <label htmlFor="endTime" className="block text-sm font-medium text-gray-700">End Time</label>
                          <input
                            type="time"
                            id="endTime"
                            name="endTime"
                            value={newAppointment.endTime}
                            onChange={handleNewAppointmentChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                            required
                          />
                        </div>
                      </div>
                      
                      <div>
                        <label htmlFor="notes" className="block text-sm font-medium text-gray-700">Notes</label>
                        <textarea
                          id="notes"
                          name="notes"
                          rows="3"
                          value={newAppointment.notes}
                          onChange={handleNewAppointmentChange}
                          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                          placeholder="Optional notes about this appointment"
                        ></textarea>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={handleScheduleFollowUp}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Schedule Appointment
                </button>
                <button
                  type="button"
                  onClick={() => setShowScheduleModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add the modal at the end of the component */}
      {selectedEvaluation && (
        <EvaluationDetailsModal
          evaluation={selectedEvaluation}
          onClose={() => setSelectedEvaluation(null)}
        />
      )}
    </div>
  );
};

export default PatientDetailsScreen;