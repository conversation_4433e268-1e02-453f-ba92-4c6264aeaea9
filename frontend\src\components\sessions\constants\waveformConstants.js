/**
 * Constants for EECP waveform display functionality.
 */

// Canvas dimensions
export const CANVAS_WIDTH = 600;
export const CANVAS_HEIGHT = 150;

// Timing constants
export const AUTO_SAVE_INTERVAL = 30000; // 30 seconds
export const UPDATE_INTERVAL = 1000; // 1 second
export const ANIMATION_SPEED = 1;

// Grid configuration
export const GRID_VERTICAL_DIVISIONS = 10;
export const GRID_HORIZONTAL_DIVISIONS = 5;

// Waveform generation
export const WAVEFORM_CYCLES = 5;
export const POINTS_PER_CYCLE = 100;

// Colors
export const COLORS = {
    ECG: '#FF4560',
    PLETHYSMOGRAM: '#00E396',
    GRID: 'rgba(0, 0, 0, 0.1)',
    BASELINE: 'rgba(0, 0, 0, 0.3)'
};

// Default parameters
export const DEFAULT_PARAMETERS = {
    cuffPressure: 220,
    inflationRatio: 1.0,
    deflationTiming: 0,
    triggerDelay: 50
};

// Parameter limits
export const PARAMETER_LIMITS = {
    cuffPressure: { min: 180, max: 300, step: 5 },
    inflationRatio: { min: 0.5, max: 1.5, step: 0.1 },
    deflationTiming: { min: -50, max: 50, step: 5 },
    triggerDelay: { min: 0, max: 100, step: 5 }
};