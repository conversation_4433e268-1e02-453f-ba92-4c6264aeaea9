/**
 * @file FunctionalTestsTab.jsx
 * Component for displaying and editing functional test assessments in post-treatment evaluation.
 * Handles NYHA/CCS classifications, left ventricular function, and exercise capacity measurements.
 */

import { Activity, Heart, Zap, Target, CheckCircle, AlertTriangle, Info } from 'lucide-react';
import { CARD_STYLES } from '../constants/commonStyles';
import { CLINICAL_CLASSIFICATIONS } from '../constants/assessmentConstants';
import {
  AssessmentInput,
  AssessmentSelect,
  AssessmentCard,
  SummaryCard,
  ClinicalNotes,
  SectionHeader
} from '../evaluation-components/commonComponents';

/**
 * NYHA class assessment component
 */
const NYHAAssessment = ({ nyhaClass }) => {
  if (!nyhaClass) return null;

  const classInfo = CLINICAL_CLASSIFICATIONS.NYHA_CLASS.find(option => option.value === nyhaClass);
  if (!classInfo) return null;

  let status = '';
  let colorClass = '';
  let Icon = Info;

  switch (nyhaClass) {
    case 'I':
      status = 'Excellent functional capacity';
      colorClass = 'border-green-500 bg-green-50 text-green-800';
      Icon = CheckCircle;
      break;
    case 'II':
      status = 'Good functional capacity';
      colorClass = 'border-blue-500 bg-blue-50 text-blue-800';
      Icon = Info;
      break;
    case 'III':
      status = 'Moderate functional limitation';
      colorClass = 'border-yellow-500 bg-yellow-50 text-yellow-800';
      Icon = AlertTriangle;
      break;
    case 'IV':
      status = 'Severe functional limitation';
      colorClass = 'border-red-500 bg-red-50 text-red-800';
      Icon = AlertTriangle;
      break;
    default:
      status = 'Unknown';
      colorClass = 'border-gray-500 bg-gray-50 text-gray-800';
      Icon = Info;
      break;
  }

  return (
    <AssessmentCard
      title="NYHA Functional Status"
      value={`Class ${nyhaClass}`}
      status={status}
      description={classInfo.description}
      icon={Icon}
      colorClass={colorClass}
    />
  );
};
/**
 * CCS class assessment component
 */
const CCSAssessment = ({ ccsClass }) => {
  if (!ccsClass) return null;

  const classInfo = CLINICAL_CLASSIFICATIONS.CCS_CLASS.find(option => option.value === ccsClass);
  if (!classInfo) return null;

  let status = '';
  let colorClass = '';
  let Icon = Info;

  switch (ccsClass) {
    case 'I':
      status = 'Excellent angina control';
      colorClass = 'border-green-500 bg-green-50 text-green-800';
      Icon = CheckCircle;
      break;
    case 'II':
      status = 'Good angina control';
      colorClass = 'border-blue-500 bg-blue-50 text-blue-800';
      Icon = Info;
      break;
    case 'III':
      status = 'Moderate angina limitation';
      colorClass = 'border-yellow-500 bg-yellow-50 text-yellow-800';
      Icon = AlertTriangle;
      break;
    case 'IV':
      status = 'Severe angina limitation';
      colorClass = 'border-red-500 bg-red-50 text-red-800';
      Icon = AlertTriangle;
      break;
    default:
      status = 'Unknown';
      colorClass = 'border-gray-500 bg-gray-50 text-gray-800';
      Icon = Info;
      break;
  }

  return (
    <AssessmentCard
      title="CCS Angina Status"
      value={`Class ${ccsClass}`}
      status={status}
      description={classInfo.description}
      icon={Icon}
      colorClass={colorClass}
    />
  );
};

/**
 * LVEF assessment component
 */
const LVEFAssessment = ({ lvef }) => {
  if (!lvef || isNaN(parseFloat(lvef))) return null;

  const value = parseFloat(lvef);
  let status = '';
  let description = '';
  let colorClass = '';
  let Icon = Info;

  if (value >= 55) {
    status = 'Normal';
    description = 'Normal left ventricular function';
    colorClass = 'border-green-500 bg-green-50 text-green-800';
    Icon = CheckCircle;
  } else if (value >= 45) {
    status = 'Mildly reduced';
    description = 'Mild left ventricular dysfunction';
    colorClass = 'border-blue-500 bg-blue-50 text-blue-800';
    Icon = Info;
  } else if (value >= 35) {
    status = 'Moderately reduced';
    description = 'Moderate left ventricular dysfunction';
    colorClass = 'border-yellow-500 bg-yellow-50 text-yellow-800';
    Icon = AlertTriangle;
  } else {
    status = 'Severely reduced';
    description = 'Severe left ventricular dysfunction';
    colorClass = 'border-red-500 bg-red-50 text-red-800';
    Icon = AlertTriangle;
  }

  return (
    <AssessmentCard
      title="Left Ventricular Function"
      value={`${value}%`}
      status={status}
      description={description}
      icon={Icon}
      colorClass={colorClass}
    />
  );
};

/**
 * 6-minute walk distance assessment component
 */
const WalkDistanceAssessment = ({ walkDistance, age, gender }) => {
  if (!walkDistance || isNaN(parseFloat(walkDistance))) return null;

  const value = parseFloat(walkDistance);
  let status = '';
  let description = '';
  let colorClass = '';
  let Icon = Info;

  // General assessment categories (simplified)
  if (value >= 450) {
    status = 'Excellent';
    description = 'Above average exercise capacity';
    colorClass = 'border-green-500 bg-green-50 text-green-800';
    Icon = CheckCircle;
  } else if (value >= 350) {
    status = 'Good';
    description = 'Good exercise capacity';
    colorClass = 'border-blue-500 bg-blue-50 text-blue-800';
    Icon = Info;
  } else if (value >= 250) {
    status = 'Fair';
    description = 'Moderate exercise capacity';
    colorClass = 'border-yellow-500 bg-yellow-50 text-yellow-800';
    Icon = AlertTriangle;
  } else {
    status = 'Poor';
    description = 'Limited exercise capacity';
    colorClass = 'border-red-500 bg-red-50 text-red-800';
    Icon = AlertTriangle;
  }

  return (
    <AssessmentCard
      title="Exercise Capacity"
      value={`${value}m`}
      status={status}
      description={description}
      icon={Icon}
      colorClass={colorClass}
    />
  );
};

/**
 * Functional status summary component
 */
const FunctionalSummary = ({ functionalTests }) => {
  const { nyhaClass, ccsClass, lvef, walkDistance } = functionalTests;

  const assessments = [
    { label: 'NYHA', value: nyhaClass },
    { label: 'CCS', value: ccsClass },
    { label: 'LVEF', value: lvef },
    { label: 'Walk Distance', value: walkDistance }
  ].filter(item => item.value && item.value.toString().trim() !== '');

  if (assessments.length === 0) {
    return (
      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
        <h4 className="text-lg font-medium text-gray-800 mb-2">Functional Status Summary</h4>
        <p className="text-gray-600">Complete assessments to see functional status summary</p>
      </div>
    );
  }

  // Determine overall functional status
  let overallStatus = 'Good';
  let statusColor = 'text-green-600';
  let recommendations = [];

  if (nyhaClass && ['III', 'IV'].includes(nyhaClass)) {
    overallStatus = 'Impaired';
    statusColor = 'text-red-600';
    recommendations.push('Consider heart failure optimization');
  }

  if (ccsClass && ['III', 'IV'].includes(ccsClass)) {
    overallStatus = 'Impaired';
    statusColor = 'text-red-600';
    recommendations.push('Consider anti-anginal therapy optimization');
  }

  if (lvef && parseFloat(lvef) < 40) {
    overallStatus = 'Impaired';
    statusColor = 'text-red-600';
    recommendations.push('Consider heart failure medications');
  }

  if (walkDistance && parseFloat(walkDistance) < 300) {
    if (overallStatus === 'Good') overallStatus = 'Limited';
    if (overallStatus === 'Good') statusColor = 'text-yellow-600';
    recommendations.push('Consider cardiac rehabilitation');
  }

  return (
    <SummaryCard
      title="Functional Status Summary"
      metrics={[
        {
          label: 'Overall Status',
          value: overallStatus,
          color: statusColor
        },
        {
          label: 'Assessments Completed',
          value: `${assessments.length}/4`,
          color: 'text-blue-600'
        }
      ]}
      recommendations={recommendations.length > 0 ? recommendations : undefined}
    />
  );
};

/**
 * Functional Tests tab component for post-treatment evaluation
 */
const FunctionalTestsTab = ({ functionalTests, onChange }) => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <SectionHeader
        title="Functional Tests Assessment"
        icon={Activity}
        iconColor="text-green-600"
      />

      {/* Functional Classifications */}
      <div className={CARD_STYLES.SECTION}>
        <h4 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
          <Target className="mr-2 text-blue-500" size={20} />
          Functional Classifications
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <AssessmentSelect
            label="NYHA Functional Class"
            value={functionalTests.nyhaClass}
            onChange={onChange}
            fieldName="nyhaClass"
            options={CLINICAL_CLASSIFICATIONS.NYHA_CLASS}
            description="New York Heart Association classification for heart failure symptoms"
          />
          <AssessmentSelect
            label="CCS Angina Class"
            value={functionalTests.ccsClass}
            onChange={onChange}
            fieldName="ccsClass"
            options={CLINICAL_CLASSIFICATIONS.CCS_CLASS}
            description="Canadian Cardiovascular Society classification for angina"
          />
        </div>

        {/* Classification Assessments */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <NYHAAssessment nyhaClass={functionalTests.nyhaClass} />
          <CCSAssessment ccsClass={functionalTests.ccsClass} />
        </div>
      </div>

      {/* Cardiac Function */}
      <div className={CARD_STYLES.SECTION}>
        <h4 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
          <Heart className="mr-2 text-red-500" size={20} />
          Cardiac Function Assessment
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <AssessmentInput
            label="Left Ventricular Ejection Fraction (LVEF)"
            value={functionalTests.lvef}
            onChange={onChange}
            fieldName="lvef"
            placeholder="55"
            unit="%"
            description="Normal range: 55-70%. Measured by echocardiography."
          />
          <div className="flex items-center">
            <LVEFAssessment lvef={functionalTests.lvef} />
          </div>
        </div>
      </div>

      {/* Exercise Capacity */}
      <div className={CARD_STYLES.SECTION}>
        <h4 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
          <Zap className="mr-2 text-orange-500" size={20} />
          Exercise Capacity Assessment
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <AssessmentInput
            label="6-Minute Walk Distance"
            value={functionalTests.walkDistance}
            onChange={onChange}
            fieldName="walkDistance"
            placeholder="400"
            unit="meters"
            description="Distance walked in 6 minutes at comfortable pace"
          />
          <div className="flex items-center">
            <WalkDistanceAssessment walkDistance={functionalTests.walkDistance} />
          </div>
        </div>
      </div>

      {/* Functional Summary */}
      <FunctionalSummary functionalTests={functionalTests} />

      {/* Clinical Notes */}
      <ClinicalNotes
        value={functionalTests.notes}
        onChange={onChange}
        placeholder="Enter clinical observations, exercise tolerance, functional limitations, or any relevant functional assessment information..."
      />
    </div>
  );
};


export default FunctionalTestsTab;