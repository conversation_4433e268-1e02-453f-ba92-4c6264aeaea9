# SyncoreV1 Backend - Supabase Integration

This is the Python FastAPI backend for SyncoreV1 with Supabase integration.

## 🚀 Quick Setup

### 1. Get Your Supabase Credentials

1. Go to your [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project
3. Go to **Settings** → **API**
4. Copy the following values:
   - **Project URL** (e.g., `https://your-project-ref.supabase.co`)
   - **anon/public key** (starts with `eyJ...`)
   - **service_role key** (starts with `eyJ...`) - Keep this secret!
5. Go to **Settings** → **Database**
6. Copy your **Database Password**

### 2. Set Up Environment Variables

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` and fill in your Supabase credentials:
   ```env
   SUPABASE_URL=https://your-project-ref.supabase.co
   SUPABASE_ANON_KEY=your-anon-key-here
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
   SUPABASE_DB_PASSWORD=your-database-password-here
   ```

### 3. Install Dependencies

```bash
cd backend_new
pip install -r app/requirements.txt
```

### 4. Run Database Setup

```bash
python setup_supabase.py
```

This will:
- Test your Supabase connection
- Create the necessary database tables
- Set up Row Level Security
- Optionally insert sample data

### 5. Start the API Server

```bash
cd app
python main.py
```

Or using uvicorn directly:
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

## 📋 API Endpoints

### Health Check
- `GET /` - Basic health check
- `GET /health` - Detailed health check with Supabase status
- `GET /test/supabase` - Test Supabase connection

### Patients
- `GET /patients` - Get all patients
- `POST /patients` - Create a new patient

## 🗄️ Database Schema

### Patients Table
```sql
patients (
    id UUID PRIMARY KEY,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    date_of_birth DATE,
    gender VARCHAR(20),
    email VARCHAR(255),
    phone VARCHAR(50),
    address JSONB,
    medical_record_number VARCHAR(100),
    primary_diagnosis TEXT,
    medical_history JSONB,
    medications JSONB,
    allergies JSONB,
    intake_reason TEXT,
    eecp_status VARCHAR(50),
    session_count INTEGER,
    vitals JSONB,
    cardiac_measurements JSONB,
    active BOOLEAN,
    notes TEXT
)
```

### EECP Sessions Table
```sql
eecp_sessions (
    id UUID PRIMARY KEY,
    patient_id UUID REFERENCES patients(id),
    session_number INTEGER,
    session_date DATE,
    duration_minutes INTEGER,
    pre_session_vitals JSONB,
    pre_session_symptoms JSONB,
    pressure_settings JSONB,
    treatment_notes TEXT,
    post_session_vitals JSONB,
    post_session_symptoms JSONB,
    session_status VARCHAR(50),
    adverse_events TEXT,
    patient_tolerance VARCHAR(50),
    technician_id VARCHAR(100),
    supervising_physician_id VARCHAR(100)
)
```

## 🔧 Usage Examples

### Creating a Patient

```python
import httpx

patient_data = {
    "first_name": "Jane",
    "last_name": "Smith",
    "date_of_birth": "1970-08-22",
    "gender": "female",
    "email": "<EMAIL>",
    "phone": "******-987-6543",
    "address": {
        "street": "456 Oak Ave",
        "city": "Vancouver",
        "province": "BC",
        "postal_code": "V6B 1A1"
    },
    "primary_diagnosis": "Stable Angina",
    "intake_reason": "EECP therapy evaluation",
    "medical_history": {
        "conditions": ["Diabetes Type 2", "Hypertension"],
        "surgeries": []
    },
    "medications": ["Metformin 500mg", "Lisinopril 10mg"],
    "allergies": ["Sulfa drugs"],
    "vitals": {
        "height": 165,
        "weight": 70,
        "blood_pressure": "130/85"
    },
    "cardiac_measurements": {
        "lvef": 50,
        "bnp": 125
    }
}

response = httpx.post("http://localhost:8000/patients", json=patient_data)
print(response.json())
```

### Getting All Patients

```python
response = httpx.get("http://localhost:8000/patients")
patients = response.json()
print(f"Found {patients['count']} patients")
```

## 🔒 Security Features

- **Row Level Security (RLS)** enabled on all tables
- **Authentication required** for all data operations
- **Environment variables** for sensitive configuration
- **CORS protection** configured for frontend integration

## 🛠️ Development

### Project Structure
```
backend_new/
├── app/
│   ├── helpers/
│   │   └── supabase_setup.py    # Supabase connection and utilities
│   ├── main.py                  # FastAPI application
│   └── requirements.txt         # Python dependencies
├── .env.example                 # Environment variables template
├── setup_supabase.py           # Database setup script
└── README.md                   # This file
```

### Adding New Endpoints

1. Import the Supabase client:
   ```python
   from helpers.supabase_setup import get_supabase
   ```

2. Use it in your endpoint:
   ```python
   @app.get("/my-endpoint")
   async def my_endpoint():
       supabase = get_supabase()
       response = supabase.table('my_table').select("*").execute()
       return response.data
   ```

### Direct Database Queries

For complex queries, you can use direct PostgreSQL access:

```python
from helpers.supabase_setup import execute_query

@app.get("/complex-query")
async def complex_query():
    results = await execute_query("""
        SELECT p.*, COUNT(s.id) as session_count
        FROM patients p
        LEFT JOIN eecp_sessions s ON p.id = s.patient_id
        GROUP BY p.id
    """)
    return results
```

## 🐛 Troubleshooting

### Connection Issues
1. Verify your `.env` file has the correct Supabase credentials
2. Check that your Supabase project is active
3. Ensure your database password is correct
4. Run `python setup_supabase.py` to test the connection

### Import Errors
1. Make sure you're in the `app` directory when running the server
2. Install all dependencies: `pip install -r requirements.txt`
3. Check Python path configuration

### Database Errors
1. Verify tables exist by running the setup script
2. Check Row Level Security policies in Supabase dashboard
3. Ensure your API keys have the correct permissions

## 📚 Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Supabase Python Client](https://github.com/supabase/supabase-py)
