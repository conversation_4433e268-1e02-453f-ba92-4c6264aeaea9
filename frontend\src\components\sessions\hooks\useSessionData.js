/**
 * Custom hook for managing session and patient data.
 * Handles data fetching and state management.
 */

import { useState, useEffect } from 'react';
import { fetchSessionById, fetchPatientById } from '../../../services/sessionService';

const INITIAL_VITALS_STATE = {
    preHeartRate: '',
    preBloodPressure: '',
    preOxygenSaturation: '',
    postHeartRate: '',
    postBloodPressure: '',
    postOxygenSaturation: ''
};

const INITIAL_SYMPTOMS_STATE = {
    chestPain: false,
    shortnessOfBreath: false,
    fatigue: false,
    dizziness: false,
    edema: false,
    palpitations: false,
    other: ''
};

/**
 * Custom hook for session data management.
 * 
 * @param {string} sessionId - Session identifier
 * @returns {Object} Session data and management functions
 */
export const useSessionData = (sessionId) => {
    const [isLoading, setIsLoading] = useState(true);
    const [session, setSession] = useState(null);
    const [patient, setPatient] = useState(null);
    const [vitals, setVitals] = useState(INITIAL_VITALS_STATE);
    const [symptoms, setSymptoms] = useState(INITIAL_SYMPTOMS_STATE);

    useEffect(() => {
        const fetchData = async () => {
            try {
                setIsLoading(true);

                const foundSession = await fetchSessionById(sessionId);
                if (foundSession) {
                    setSession(foundSession);

                    const foundPatient = await fetchPatientById(foundSession.patientId);
                    if (foundPatient) {
                        setPatient(foundPatient);
                    }
                }
            } catch (error) {
                console.error('Error fetching session data:', error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchData();
    }, [sessionId]);

    /**
     * Updates vital signs data.
     * 
     * @param {string} name - Field name
     * @param {string} value - Field value
     */
    const updateVitals = (name, value) => {
        setVitals(prev => ({
            ...prev,
            [name]: value
        }));
    };

    /**
     * Updates symptoms data.
     * 
     * @param {string} name - Field name
     * @param {string|boolean} value - Field value
     * @param {string} type - Input type (checkbox or text)
     */
    const updateSymptoms = (name, value, type) => {
        setSymptoms(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? value : value
        }));
    };

    return {
        isLoading,
        session,
        patient,
        vitals,
        symptoms,
        updateVitals,
        updateSymptoms,
        setVitals
    };
};