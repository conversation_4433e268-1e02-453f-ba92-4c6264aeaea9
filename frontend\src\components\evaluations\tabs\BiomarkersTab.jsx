/**
 * @file BiomarkersTab.jsx
 * Component for displaying and editing biomarker values in post-treatment evaluation.
 * Handles cardiac biomarkers, inflammatory markers, and lipid profile data entry and analysis.
 */


import { Activity, Heart, Droplet, TrendingUp, AlertTriangle, CheckCircle } from 'lucide-react';
import { CARD_STYLES } from '../constants/commonStyles';
import { REFERENCE_RANGES } from '../constants/assessmentConstants';
import {
  AssessmentInput,
  StatusIndicator,
  SummaryCard,
  ClinicalNotes,
  SectionHeader
} from '../evaluation-components/commonComponents';
import { evaluateBiomarkers } from '../utils/assessmentUtils';

/**
 * Cardiovascular risk assessment based on lipid profile
 */
const CardiovascularRiskAssessment = ({ biomarkers }) => {
  const { cholesterolTotal, cholesterolLdl, cholesterolHdl, triglycerides } = biomarkers;

  if (!cholesterolTotal && !cholesterolLdl && !cholesterolHdl && !triglycerides) {
    return null;
  }

  let riskFactors = 0;
  let riskMessages = [];

  if (cholesterolTotal && parseFloat(cholesterolTotal) > 240) {
    riskFactors++;
    riskMessages.push('High total cholesterol');
  }
  if (cholesterolLdl && parseFloat(cholesterolLdl) > 160) {
    riskFactors++;
    riskMessages.push('High LDL cholesterol');
  }
  if (cholesterolHdl && parseFloat(cholesterolHdl) < 40) {
    riskFactors++;
    riskMessages.push('Low HDL cholesterol');
  }
  if (triglycerides && parseFloat(triglycerides) > 200) {
    riskFactors++;
    riskMessages.push('High triglycerides');
  }

  let riskLevel = 'Low';
  let colorClass = 'text-green-600 bg-green-50 border-green-200';
  let Icon = CheckCircle;

  if (riskFactors >= 3) {
    riskLevel = 'High';
    colorClass = 'text-red-600 bg-red-50 border-red-200';
    Icon = AlertTriangle;
  } else if (riskFactors >= 1) {
    riskLevel = 'Moderate';
    colorClass = 'text-yellow-600 bg-yellow-50 border-yellow-200';
    Icon = TrendingUp;
  }

  return (
    <div className={`p-4 rounded-lg border ${colorClass} mt-4`}>
      <div className="flex items-center mb-2">
        <Icon size={20} className="mr-2" />
        <h5 className="font-medium">Cardiovascular Risk Assessment: {riskLevel}</h5>
      </div>
      {riskMessages.length > 0 && (
        <div className="text-sm">
          <p className="opacity-75">Risk factors identified:</p>
          <ul className="list-disc list-inside text-xs mt-1 opacity-75">
            {riskMessages.map((message, index) => (
              <li key={index}>{message}</li>
            ))}
          </ul>
        </div>
      )}
      {riskLevel === 'Low' && (
        <p className="text-sm opacity-75">Lipid profile within acceptable ranges</p>
      )}
    </div>
  );
};

/**
 * Overall biomarkers summary
 */
const BiomarkersSummary = ({ biomarkers }) => {
  const { totalMeasured, withinNormalRange, normalPercentage } = evaluateBiomarkers(biomarkers);

  if (totalMeasured === 0) {
    return (
      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
        <h4 className="text-lg font-medium text-gray-800 mb-2">Biomarkers Summary</h4>
        <p className="text-gray-600">Enter biomarker values to see analysis</p>
      </div>
    );
  }

  return (
    <SummaryCard
      title="Biomarkers Summary"
      metrics={[
        {
          label: 'Total Measured',
          value: totalMeasured,
          color: 'text-blue-600'
        },
        {
          label: 'Within Normal Range',
          value: withinNormalRange,
          color: 'text-green-600'
        },
        {
          label: 'Normal Values',
          value: `${normalPercentage}%`,
          color: 'text-gray-600'
        }
      ]}
    />
  );
};


/**
 * Biomarkers tab component for post-treatment evaluation
 */
const BiomarkersTab = ({ biomarkers, onChange }) => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <SectionHeader
        title="Biomarkers Assessment"
        icon={Activity}
        iconColor="text-purple-600"
      />

      {/* Cardiac Biomarkers */}
      <div className={CARD_STYLES.SECTION}>
        <h4 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
          <Heart className="mr-2 text-red-500" size={20} />
          Cardiac Biomarkers
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <AssessmentInput
            label="B-Type Natriuretic Peptide (BNP)"
            value={biomarkers.bnp}
            onChange={onChange}
            fieldName="bnp"
            placeholder="85"
            unit="pg/mL"
            description="Indicates heart failure when elevated"
          />
          <AssessmentInput
            label="Troponin I"
            value={biomarkers.troponin}
            onChange={onChange}
            fieldName="troponin"
            placeholder="0.5"
            unit="ng/L"
            description="Indicates cardiac muscle damage when elevated"
          />
        </div>

        {/* Status indicators */}
        <StatusIndicator
          value={biomarkers.bnp}
          range={REFERENCE_RANGES.BIOMARKERS.bnp}
          label={REFERENCE_RANGES.BIOMARKERS.bnp.name}
          unit={REFERENCE_RANGES.BIOMARKERS.bnp.unit}
          fieldName="bnp"
        />
        <StatusIndicator
          value={biomarkers.troponin}
          range={REFERENCE_RANGES.BIOMARKERS.troponin}
          label={REFERENCE_RANGES.BIOMARKERS.troponin.name}
          unit={REFERENCE_RANGES.BIOMARKERS.troponin.unit}
          fieldName="troponin"
        />
      </div>

      {/* Basic Chemistry */}
      <div className={CARD_STYLES.SECTION}>
        <h4 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
          <Droplet className="mr-2 text-blue-500" size={20} />
          Basic Chemistry & Hematology
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <AssessmentInput
            label="Creatinine"
            value={biomarkers.creatinine}
            onChange={onChange}
            fieldName="creatinine"
            placeholder="1.0"
            unit="mg/dL"
            description="Indicates kidney function"
          />
          <AssessmentInput
            label="Hemoglobin"
            value={biomarkers.hemoglobin}
            onChange={onChange}
            fieldName="hemoglobin"
            placeholder="14.0"
            unit="g/dL"
            description="Indicates oxygen-carrying capacity"
          />
        </div>

        {/* Status indicators */}
        <StatusIndicator
          value={biomarkers.creatinine}
          range={REFERENCE_RANGES.BIOMARKERS.creatinine}
          label={REFERENCE_RANGES.BIOMARKERS.creatinine.name}
          unit={REFERENCE_RANGES.BIOMARKERS.creatinine.unit}
          fieldName="creatinine"
        />
        <StatusIndicator
          value={biomarkers.hemoglobin}
          range={REFERENCE_RANGES.BIOMARKERS.hemoglobin}
          label={REFERENCE_RANGES.BIOMARKERS.hemoglobin.name}
          unit={REFERENCE_RANGES.BIOMARKERS.hemoglobin.unit}
          fieldName="hemoglobin"
        />
      </div>

      {/* Lipid Profile */}
      <div className={CARD_STYLES.SECTION}>
        <h4 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
          <TrendingUp className="mr-2 text-green-500" size={20} />
          Lipid Profile
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <AssessmentInput
            label="Total Cholesterol"
            value={biomarkers.cholesterolTotal}
            onChange={onChange}
            fieldName="cholesterolTotal"
            placeholder="180"
            unit="mg/dL"
            description="Target: < 200 mg/dL"
          />
          <AssessmentInput
            label="LDL Cholesterol"
            value={biomarkers.cholesterolLdl}
            onChange={onChange}
            fieldName="cholesterolLdl"
            placeholder="90"
            unit="mg/dL"
            description="Target: < 100 mg/dL"
          />
          <AssessmentInput
            label="HDL Cholesterol"
            value={biomarkers.cholesterolHdl}
            onChange={onChange}
            fieldName="cholesterolHdl"
            placeholder="50"
            unit="mg/dL"
            description="Target: ≥ 40 mg/dL (men), ≥ 50 mg/dL (women)"
          />
          <AssessmentInput
            label="Triglycerides"
            value={biomarkers.triglycerides}
            onChange={onChange}
            fieldName="triglycerides"
            placeholder="120"
            unit="mg/dL"
            description="Target: < 150 mg/dL"
          />
        </div>

        {/* Lipid status indicators */}
        <StatusIndicator
          value={biomarkers.cholesterolTotal}
          range={REFERENCE_RANGES.BIOMARKERS.cholesterolTotal}
          label={REFERENCE_RANGES.BIOMARKERS.cholesterolTotal.name}
          unit={REFERENCE_RANGES.BIOMARKERS.cholesterolTotal.unit}
          fieldName="cholesterolTotal"
        />
        <StatusIndicator
          value={biomarkers.cholesterolLdl}
          range={REFERENCE_RANGES.BIOMARKERS.cholesterolLdl}
          label={REFERENCE_RANGES.BIOMARKERS.cholesterolLdl.name}
          unit={REFERENCE_RANGES.BIOMARKERS.cholesterolLdl.unit}
          fieldName="cholesterolLdl"
        />
        <StatusIndicator
          value={biomarkers.cholesterolHdl}
          range={REFERENCE_RANGES.BIOMARKERS.cholesterolHdl}
          label={REFERENCE_RANGES.BIOMARKERS.cholesterolHdl.name}
          unit={REFERENCE_RANGES.BIOMARKERS.cholesterolHdl.unit}
          fieldName="cholesterolHdl"
        />
        <StatusIndicator
          value={biomarkers.triglycerides}
          range={REFERENCE_RANGES.BIOMARKERS.triglycerides}
          label={REFERENCE_RANGES.BIOMARKERS.triglycerides.name}
          unit={REFERENCE_RANGES.BIOMARKERS.triglycerides.unit}
          fieldName="triglycerides"
        />

        {/* Cardiovascular risk assessment */}
        <CardiovascularRiskAssessment biomarkers={biomarkers} />
      </div>

      {/* Summary */}
      <BiomarkersSummary biomarkers={biomarkers} />

      {/* Clinical Notes */}
      <ClinicalNotes
        value={biomarkers.notes}
        onChange={onChange}
        placeholder="Enter clinical observations, trends, lab interpretation, or any relevant biomarker information..."
      />
    </div>
  );
};


export default BiomarkersTab;