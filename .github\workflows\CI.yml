name: Full CI

on:
  push:
    branches:
      - main
      - feature/**
  pull_request:
    branches:
      - main

jobs:
  test-backend:
    runs-on: ubuntu-latest

    # default to running all commands from the ./backend directory
    defaults:
      run:
        working-directory: backend
    steps:
      - uses: actions/checkout@v3

      - name: Install dependencies
        run: npm install


  test-frontend:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: frontend

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Install dependencies
        run: npm install
                 