/**
 * @file sessionConstants.js
 * Constants for EECP session management and workflow.
 */

// Session stages
export const SESSION_STAGES = {
    PRE_SESSION: 'pre-session',
    ACTIVE: 'active',
    DURING_15: 'during-15',
    DURING_45: 'during-45',
    POST_SESSION: 'post-session',
    COMPLETE: 'complete'
};

// Timer constants
export const TIMER_INTERVALS = {
    UPDATE_INTERVAL: 1000, // 1 second
    CHECKPOINT_15_MIN: 900, // 15 minutes in seconds
    CHECKPOINT_45_MIN: 2700 // 45 minutes in seconds
};

// API base URL
export const API_BASE_URL = 'http://localhost:4000';

// Default session data structure
export const DEFAULT_SESSION_DATA = {
    sessionNumber: 1,
    date: new Date().toISOString().split('T')[0],
    startTime: '',
    endTime: '',
    duration: 0,
    status: 'in-progress',
    measurements: {
        preEECP: {
            timestamp: '',
            o2Liters: '',
            weight: '',
            weightUnit: 'lbs',
            bloodPressure: { systolic: '', diastolic: '' },
            pulseRate: '',
            spo2: ''
        },
        during15Min: {
            timestamp: '',
            dsPeak: '',
            dsArea: '',
            pulseRate: '',
            appliedPressure: ''
        },
        during45Min: {
            timestamp: '',
            dsPeak: '',
            dsArea: '',
            pulseRate: '',
            appliedPressure: ''
        },
        postEECP: {
            timestamp: '',
            bloodPressure: { systolic: '', diastolic: '' },
            pulseRate: '',
            spo2: '',
            skinCondition: 'Normal - No irritation',
            electrodeInducedArtefact: false
        }
    },
    personnel: {
        therapist: '',
        physician: ''
    },
    notes: '',
    adverseEvents: [],
    interruptions: []
};

// Skin condition options
export const SKIN_CONDITIONS = [
    'Normal - No irritation',
    'Mild irritation',
    'Moderate irritation',
    'Severe irritation'
];

// Weight units
export const WEIGHT_UNITS = [
    { value: 'lbs', label: 'lbs' },
    { value: 'kg', label: 'kg' }
];

// Validation patterns
export const VALIDATION_PATTERNS = {
    NUMERIC: /^\d+$/,
    DECIMAL: /^\d*\.?\d*$/,
    PATIENT_ID: /^[A-Za-z0-9]{10}-]+$/
};