import { z } from 'zod';

/**
 * @file Defines Zod schemas and TypeScript types for authentication-related data.
 */

// Define schemas for request bodies
export const LoginRequestSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters long"),
});
/**
 * TypeScript type inferred from LoginRequestSchema.
 * Represents the structure of a login request payload.
 */
export type LoginRequest = z.infer<typeof LoginRequestSchema>;

// Define schemas for response bodies
export const TokenResponseSchema = z.object({
  access_token: z.string(),
  refresh_token: z.string(),
  expires_in: z.number().optional(), // seconds until access token expires
});
export type TokenResponse = z.infer<typeof TokenResponseSchema>;

// Generic error response schema
export const ErrorResponseSchema = z.object({
  error: z.string(),
  details: z.any().optional(),
});
export type ErrorResponse = z.infer<typeof ErrorResponseSchema>;
