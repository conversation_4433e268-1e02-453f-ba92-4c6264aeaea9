/**
 * @file patientRoutes.ts
 * @description Express routes for patient-related operations.
 * Provides CRUD endpoints with both FHIR-compliant and frontend-optimized responses.
 */

import { Router, Request, Response } from 'express';
import { PatientCreate, PatientUpdate } from './patientSchemas.js';
import * as patientService from './patientService.js';
import { getMedplumClient } from '../helpers/medplumHelper.js';

// Constants
const MAX_PATIENTS_PER_REQUEST = 100;
const DEFAULT_PATIENT_LIMIT = 20;

const router = Router();

/**
 * Calculates age from birth date.
 * 
 * @param {string} birthDate - Birth date in YYYY-MM-DD format
 * @returns {number|null} Age in years or null if no birth date
 */
function calculateAge(birthDate: string): number | null {
    if (!birthDate) return null;

    const birth = new Date(birthDate);
    const today = new Date();
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
    }

    return age;
}

/**
 * Formats patient data for frontend consumption.
 * 
 * @param {any} patient - FHIR Patient resource
 * @returns {Object} Frontend-optimized patient summary
 */
function formatPatientSummary(patient: any) {
    const age = calculateAge(patient.birthDate);
    const name = patient.name?.[0]
        ? `${patient.name[0].given?.join(' ')} ${patient.name[0].family}`
        : 'Unknown';

    return {
        id: patient.id,
        name,
        family: patient.name?.[0]?.family || '',
        given: patient.name?.[0]?.given || [],
        gender: patient.gender,
        birthDate: patient.birthDate,
        age,
        status: patient.active !== false ? 'active' : 'inactive',
        metrics: {
            ccsClass: { current: 'N/A' },
            lvef: { current: 'N/A' },
            anginaEpisodes: { current: 'N/A' },
            bp: { current: 'N/A' }
        },
        summary: 'Patient profile available.',
        meta: patient.meta
    };
}

/**
 * Creates a new patient in the system.
 * 
 * @route POST /api/patients
 * @param {Request} req - Express request object containing patient data
 * @param {Response} res - Express response object
 * @returns {Promise<Response>} JSON response with created patient data
 */
router.post('/', async (req: Request, res: Response): Promise<any> => {
    try {
        const patientData: PatientCreate = req.body;

        // Validate required fields
        if (!patientData.family ||
            !patientData.given ||
            patientData.given.length === 0) {
            return res.status(400).json({
                error: 'Missing required fields',
                message: 'family and given names are required',
                required: ['family', 'given']
            });
        }

        // Get Medplum client
        const client = await getMedplumClient();

        // Create patient
        const patient = await patientService.createPatient(client, patientData);

        console.log(`Patient created: ${patient.id}`);
        res.status(201).json({
            success: true,
            message: 'Patient created successfully',
            data: patient
        });

    } catch (error: any) {
        console.error(' Error creating patient:', error.message);
        res.status(400).json({
            error: 'Failed to create patient',
            message: error.message,
            details: error.response?.data || null
        });
    }
});

/**
 * Retrieves a patient by their unique ID.
 * 
 * @route GET /api/patients/:id
 * @param {Request} req - Express request object with patient ID in params
 * @param {Response} res - Express response object
 * @returns {Promise<Response>} JSON response with patient data
 */
router.get('/:id', async (req: Request, res: Response): Promise<any> => {
    try {
        const patientId = req.params.id;
        const format = req.query.format as string;

        if (!patientId) {
            return res.status(400).json({
                error: 'Patient ID is required'
            });
        }

        // Get Medplum client
        const client = await getMedplumClient();

        // Get patient
        const patient = await patientService.getPatient(client, patientId);

        if (!patient) {
            return res.status(404).json({
                error: 'Patient not found',
                patientId
            });
        }

        console.log(`Patient retrieved: ${patient.id}`);

        // Return different formats based on query parameter
        if (format === 'summary') {
            res.json(formatPatientSummary(patient));
        } else {
            res.json({
                success: true,
                data: patient
            });
        }

    } catch (error: any) {
        console.error(' Error retrieving patient:', error.message);

        if (error.message.includes('not found') || error.status === 404) {
            res.status(404).json({
                error: 'Patient not found',
                patientId: req.params.id
            });
        } else {
            res.status(500).json({
                error: 'Failed to retrieve patient',
                message: error.message
            });
        }
    }
});

/**
 * Updates an existing patient's information.
 * 
 * @route PATCH /api/patients/:id
 * @param {Request} req - Express request object with patient ID and update data
 * @param {Response} res - Express response object
 * @returns {Promise<Response>} JSON response with updated patient data
 */
router.patch('/:id', async (req: Request, res: Response): Promise<any> => {
    try {
        const patientId = req.params.id;
        const updateData: PatientUpdate = req.body;

        if (!patientId) {
            return res.status(400).json({
                error: 'Patient ID is required'
            });
        }

        // Get Medplum client
        const client = await getMedplumClient();

        // Update patient
        const updatedPatient = await patientService.updatePatient(
            client,
            patientId,
            updateData
        );

        console.log(`Patient updated: ${updatedPatient.id}`);
        res.json({
            success: true,
            message: 'Patient updated successfully',
            data: updatedPatient
        });

    } catch (error: any) {
        console.error(' Error updating patient:', error.message);

        if (error.message.includes('not found') || error.status === 404) {
            res.status(404).json({
                error: 'Patient not found',
                patientId: req.params.id
            });
        } else {
            res.status(400).json({
                error: 'Failed to update patient',
                message: error.message
            });
        }
    }
});

/**
 * Deletes a patient from the system.
 * 
 * @route DELETE /api/patients/:id
 * @param {Request} req - Express request object with patient ID
 * @param {Response} res - Express response object
 * @returns {Promise<Response>} Empty response with 204 status
 */
router.delete('/:id', async (req: Request, res: Response): Promise<any> => {
    try {
        const patientId = req.params.id;

        if (!patientId) {
            return res.status(400).json({
                error: 'Patient ID is required'
            });
        }

        // Get Medplum client
        const client = await getMedplumClient();

        // Delete patient
        await patientService.deletePatient(client, patientId);

        console.log(`Patient deleted: ${patientId}`);
        res.status(204).send();

    } catch (error: any) {
        console.error(' Error deleting patient:', error.message);

        if (error.message.includes('not found') || error.status === 404) {
            res.status(404).json({
                error: 'Patient not found',
                patientId: req.params.id
            });
        } else {
            res.status(500).json({
                error: 'Failed to delete patient',
                message: error.message
            });
        }
    }
});

/**
 * Retrieves a paginated list of patients with optional search and filtering.
 * 
 * @route GET /api/patients
 * @param {Request} req - Express request object with query parameters
 * @param {Response} res - Express response object
 * @returns {Promise<Response>} JSON response with patient list and pagination
 */
router.get('/', async (req: Request, res: Response) => {
    try {
        const limit = Math.min(
            parseInt(req.query.limit as string) || DEFAULT_PATIENT_LIMIT,
            MAX_PATIENTS_PER_REQUEST
        );
        const offset = parseInt(req.query.offset as string) || 0;
        const search = req.query.search as string;
        const doctorId = req.query.doctorId as string;
        const format = req.query.format as string;

        // Get Medplum client
        const client = await getMedplumClient();

        // Build search parameters
        const searchParams: any = { search, limit, offset };

        // Add doctor filtering if provided
        if (doctorId) {
            // Use the underlying Medplum client for doctor filtering
            const searchParamsForMedplum: Record<string, string> = {
                _count: limit.toString(),
                _offset: offset.toString()
            };

            searchParamsForMedplum['general-practitioner'] = `Practitioner/${doctorId}`;

            if (search) {
                searchParamsForMedplum.name = search;
            }

            const result = await client.search('Patient', searchParamsForMedplum);
            const patients = (result.entry || []).map((e: any) => e.resource);

            const formattedPatients = format === 'summary'
                ? patients.map(formatPatientSummary)
                : patients;

            res.json({
                success: true,
                data: formattedPatients,
                pagination: {
                    total: result.total || patients.length,
                    limit,
                    offset,
                    hasMore: (result.total || 0) > (offset + patients.length)
                }
            });
            return;
        }

        // Standard search using service layer
        const searchResult = await patientService.searchPatients(client, searchParams);

        console.log(`${searchResult.patients.length} patients retrieved`);

        // Format data based on query parameter
        const responseData = format === 'summary'
            ? searchResult.patients.map(formatPatientSummary)
            : searchResult.patients;

        res.json({
            success: true,
            data: responseData,
            pagination: {
                total: searchResult.total,
                limit,
                offset,
                hasMore: searchResult.hasMore
            }
        });

    } catch (error: any) {
        console.error(' Error listing patients:', error.message);
        res.status(500).json({
            error: 'Failed to retrieve patients',
            message: error.message
        });
    }
});

/**
 * Health check endpoint for the patients API.
 * 
 * @route GET /api/patients/health
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @returns {Promise<Response>} JSON response with service health status
 */
router.get('/health', async (req: Request, res: Response) => {
    try {
        // Test Medplum connection
        const client = await getMedplumClient();

        // Verify connection with minimal search
        const testResult = await patientService.searchPatients(client, {
            limit: 1
        });

        res.json({
            status: 'OK',
            service: 'Patients API',
            timestamp: new Date().toISOString(),
            medplum: {
                connected: true,
                baseUrl: process.env.MEDPLUM_BASE_URL ||
                    'https://api.medplum.com/',
                testQueryResults: testResult.patients.length
            },
            endpoints: {
                'POST /': 'Create patient',
                'GET /': 'List patients (add ?format=summary for frontend format)',
                'GET /:id': 'Get patient by ID (add ?format=summary for frontend format)',
                'PATCH /:id': 'Update patient',
                'DELETE /:id': 'Delete patient',
            },
            features: {
                doctorFiltering: 'Add ?doctorId=<practitioner-id> to filter by doctor',
                frontendFormat: 'Add ?format=summary for frontend-optimized response',
                pagination: 'Use ?limit=<num>&offset=<num> for pagination',
                search: 'Use ?search=<term> to search by name'
            }
        });
    } catch (error: any) {
        res.status(500).json({
            status: 'ERROR',
            service: 'Patients API',
            error: error.message,
            medplum: {
                connected: false,
                error: error.message
            }
        });
    }
});

export default router;