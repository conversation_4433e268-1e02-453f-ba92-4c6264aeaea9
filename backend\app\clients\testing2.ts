import { MedplumRevamp } from './refactor_medplum';
import { Practitioner } from '@medplum/fhirtypes';

async function main() {
    const medplum = new MedplumRevamp();
    await medplum.initializeMedplum();

    const practitioner: Practitioner = {
        resourceType: 'Practitioner',
        name: [{ family: '<PERSON>', given: ['<PERSON>'], prefix: ['Dr.'] }],
    }

    const created_patient = await medplum.create(practitioner);
    console.log(created_patient);

}

main();