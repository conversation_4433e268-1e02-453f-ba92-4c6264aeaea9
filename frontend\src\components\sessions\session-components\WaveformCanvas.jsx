/**
 * Individual canvas component for displaying ECG or plethysmogram waveforms.
 */

import { useRef, useEffect } from 'react';
import { CANVAS_WIDTH, CANVAS_HEIGHT } from '../constants/waveformConstants';
import { optimizeCanvasForHiDPI } from '../utils/canvasUtils';

/**
 * Renders a single waveform canvas with proper labeling and styling.
 * Handles canvas setup, HiDPI optimization, and provides ref access for animation.
 *
 * @param {Object} props
 * @param {string} props.type - Waveform type ('ecg' or 'plethysmogram')
 * @param {string} props.label - Display label for the waveform
 * @param {string} props.color - Color indicator for the waveform type
 * @param {Function} props.onCanvasReady - Callback when canvas ref is ready
 */
const WaveformCanvas = ({ type, label, color, onCanvasReady }) => {
    const canvasRef = useRef(null);

    /**
     * Color mapping for different waveform types.
     */
    const colorMap = {
        ecg: 'bg-red-500',
        plethysmogram: 'bg-green-500'
    };

    // Initialize canvas when component mounts
    useEffect(() => {
        if (canvasRef.current) {
            const canvas = canvasRef.current;
            const ctx = canvas.getContext('2d');

            // Optimize for high DPI displays
            optimizeCanvasForHiDPI(ctx, canvas, CANVAS_WIDTH, CANVAS_HEIGHT);

            // Notify parent component that canvas is ready
            if (onCanvasReady) {
                onCanvasReady(canvasRef);
            }
        }
    }, [onCanvasReady]);

    return (
        <div className="waveform-canvas-container">
            {/* Waveform label */}
            <div className="flex items-center mb-1">
                <div className={`h-3 w-3 rounded-full mr-2 ${colorMap[type] || color}`} />
                <span className="text-sm font-medium text-gray-700">
                    {label}
                </span>
            </div>

            {/* Canvas container with styling */}
            <div className="bg-gray-50 rounded border border-gray-200 overflow-hidden">
                <canvas
                    ref={canvasRef}
                    width={CANVAS_WIDTH}
                    height={CANVAS_HEIGHT}
                    className="w-full h-auto block"
                    style={{
                        display: 'block',
                        maxWidth: '100%',
                        height: 'auto'
                    }}
                />
            </div>
        </div>
    );
};

export default WaveformCanvas;